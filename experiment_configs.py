def define_algorithm_comparison_config():
    """定义算法对比实验配置"""
    configs = []

    algorithms = ['ppo', 'a2c', 'dqn']
    for algo in algorithms:
        configs.append({
            'name': f'algorithm_{algo}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': algo,
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42
            }
        })

    return configs


def define_gnn_comparison_config():
    """定义GNN架构对比实验配置"""
    configs = []

    gnn_types = ['gat', 'gcn', 'gin']
    for gnn_type in gnn_types:
        configs.append({
            'name': f'gnn_{gnn_type}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': gnn_type,
                'shared_reward': True,
                'seed': 42
            }
        })

    return configs


def define_agent_mode_comparison_config():
    """定义智能体模式对比实验配置"""
    configs = [
        {
            'name': 'control_based',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42
            }
        },
        {
            'name': 'function_based',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'function_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42
            }
        }
    ]

    return configs


def define_graph_strategy_comparison_config():
    """定义图构建策略对比实验配置"""
    configs = []

    strategies = ['physical_topology', 'functional_relation', 'spatiotemporal']
    for strategy in strategies:
        configs.append({
            'name': f'graph_{strategy}',
            'config': {
                'graph_strategy': strategy,
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42
            }
        })

    return configs


def define_workload_adaptation_config():
    """定义工况适应性实验配置"""
    configs = []

    # 工况类型及其详细配置
    workload_configs = {
        'normal': {
            'feed_conc': {'A': 0.5, 'B': 0.5},
            'disturbance': False,
            'description': '标准工况 - 均等进料浓度'
        },
        'high_feed': {
            'feed_conc': {'A': 0.7, 'B': 0.3},
            'disturbance': False,
            'description': '高A组分进料工况'
        },
        'low_feed': {
            'feed_conc': {'A': 0.3, 'B': 0.7},
            'disturbance': False,
            'description': '低A组分进料工况'
        },
        'disturbed': {
            'feed_conc': {'A': 0.5, 'B': 0.5},
            'disturbance': True,
            'disturbance_range': {
                'kh': (0.7, 1.3),
                'D': (0.8, 1.2)
            },
            'description': '参数扰动工况 - 系统参数随机波动'
        }
    }

    # 为每种工况创建实验配置
    for workload_type, workload_details in workload_configs.items():
        configs.append({
            'name': f'workload_{workload_type}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'workload_type': workload_type,
                'workload_details': workload_details,
                'seed': 42
            }
        })

    return configs


def define_pareto_experiment_config():
    """定义帕累托前沿分析实验配置"""
    configs = [
        {
            'name': 'purity_focus',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'purity_weight': 1.5,
                'productivity_weight': 0.5,
                'seed': 42
            }
        },
        {
            'name': 'balanced',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'purity_weight': 1.0,
                'productivity_weight': 1.0,
                'seed': 42
            }
        },
        {
            'name': 'productivity_focus',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'purity_weight': 0.5,
                'productivity_weight': 1.5,
                'seed': 42
            }
        }
    ]

    return configs


def define_standard_experiment_config():
    """定义标准单一实验配置"""
    return [
        {
            'name': 'standard',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42
            }
        }
    ]


def define_comprehensive_experiment_config():
    """定义全面实验配置"""
    return [
        {
            'name': 'optimal_config',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42,
                'hidden_dim': 128,
                'output_dim': 128,
                'lr': 1e-4,
                'gamma': 0.995,
                'temporal_window': 10,
                'record_performance_metrics': True
            }
        },
        {
            'name': 'baseline_no_graph',
            'config': {
                'graph_strategy': 'physical_topology',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gcn',
                'shared_reward': True,
                'seed': 42,
                'hidden_dim': 64,
                'output_dim': 64,
                'record_performance_metrics': True
            }
        }
    ]


def define_ablation_experiments():
    """定义消融实验配置"""
    experiments = []

    # 1. 比较不同图构建策略
    graph_strategies = ['physical_topology', 'functional_relation', 'spatiotemporal']
    for strategy in graph_strategies:
        experiments.append({
            'name': f'graph_strategy_{strategy}',
            'config': {
                'graph_strategy': strategy,
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42,
                'record_performance_metrics': True
            }
        })

    # 2. 比较不同MARL算法
    algorithms = ['dqn', 'a2c', 'ppo']
    for algo in algorithms:
        experiments.append({
            'name': f'algorithm_{algo}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': algo,
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42,
                'record_performance_metrics': True
            }
        })

    # 3. 比较不同智能体模式
    agent_modes = ['control_based', 'function_based']
    for mode in agent_modes:
        experiments.append({
            'name': f'agent_mode_{mode}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': mode,
                'gnn_type': 'gat',
                'shared_reward': True,
                'seed': 42,
                'record_performance_metrics': True
            }
        })

    # 4. 比较不同GNN类型
    gnn_types = ['gat', 'gcn', 'gin']
    for gnn_type in gnn_types:
        experiments.append({
            'name': f'gnn_type_{gnn_type}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': gnn_type,
                'shared_reward': True,
                'seed': 42,
                'record_performance_metrics': True
            }
        })

    # 5. 比较共享奖励与非共享奖励
    reward_types = [True, False]
    for shared in reward_types:
        experiments.append({
            'name': f'shared_reward_{shared}',
            'config': {
                'graph_strategy': 'spatiotemporal',
                'algorithm': 'ppo',
                'agent_mode': 'control_based',
                'gnn_type': 'gat',
                'shared_reward': shared,
                'seed': 42,
                'record_performance_metrics': True
            }
        })

    return experiments


def define_mini_experiment_config(experiment_type='algorithm'):
    """定义迷你版实验配置"""
    if experiment_type == 'algorithm':
        # 算法对比
        configs = []
        algorithms = ['ppo', 'dqn']
        for algo in algorithms:
            configs.append({
                'name': f'algorithm_{algo}',
                'config': {
                    'graph_strategy': 'spatiotemporal',
                    'algorithm': algo,
                    'agent_mode': 'control_based',
                    'gnn_type': 'gat',
                    'seed': 42
                }
            })
        return configs

    elif experiment_type == 'graph':
        # 图构建策略对比
        configs = []
        strategies = ['physical_topology', 'spatiotemporal']
        for strategy in strategies:
            configs.append({
                'name': f'graph_{strategy}',
                'config': {
                    'graph_strategy': strategy,
                    'algorithm': 'ppo',
                    'agent_mode': 'control_based',
                    'gnn_type': 'gat',
                    'seed': 42
                }
            })
        return configs

    elif experiment_type == 'agent':
        # 智能体模式对比
        return [
            {
                'name': 'control_based',
                'config': {
                    'graph_strategy': 'spatiotemporal',
                    'algorithm': 'ppo',
                    'agent_mode': 'control_based',
                    'gnn_type': 'gat',
                    'seed': 42
                }
            },
            {
                'name': 'function_based',
                'config': {
                    'graph_strategy': 'spatiotemporal',
                    'algorithm': 'ppo',
                    'agent_mode': 'function_based',
                    'gnn_type': 'gat',
                    'seed': 42
                }
            }
        ]
    else:
        # 默认运行单一实验
        return [
            {
                'name': 'standard',
                'config': {
                    'graph_strategy': 'spatiotemporal',
                    'algorithm': 'ppo',
                    'agent_mode': 'control_based',
                    'gnn_type': 'gat',
                    'seed': 42
                }
            }
        ]