import datetime
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd


def setup_publication_style():
    """设置适合科学出版物的绘图风格"""
    # 使用seaborn的白色网格风格作为基础
    sns.set(style="whitegrid")

    # 全局字体设置
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['legend.title_fontsize'] = 14

    # 线条设置
    plt.rcParams['lines.linewidth'] = 2
    plt.rcParams['axes.linewidth'] = 1.5
    plt.rcParams['xtick.major.width'] = 1.2
    plt.rcParams['ytick.major.width'] = 1.2

    # 网格设置
    plt.rcParams['grid.linewidth'] = 0.8
    plt.rcParams['grid.alpha'] = 0.3

    # 图像设置
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['savefig.bbox'] = 'tight'
    plt.rcParams['savefig.pad_inches'] = 0.1


def get_publication_colors(n_colors=8):
    """获取用于出版物的颜色方案"""
    # 科学出版物常用的高对比度配色
    colors = [
        "#0173B2", "#DE8F05", "#029E73", "#D55E00",
        "#CC78BC", "#CA9161", "#FBAFE4", "#949494"
    ]
    return colors[:n_colors]


def create_timestamp_folder(base_dir):
    """创建带时间戳的文件夹以避免覆盖"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_path = os.path.join(base_dir, f"results_{timestamp}")
    os.makedirs(folder_path, exist_ok=True)
    return folder_path


def plot_metric(all_runs, metric_name, metric_title, save_dir, exp_name):
    """绘制单个性能指标"""
    # 设置绘图风格
    setup_publication_style()

    # 创建图形
    plt.figure(figsize=(10, 6))

    # 收集所有运行的数据并处理
    all_values = [run.get(metric_name, []) for run in all_runs]
    valid_runs = [values for values in all_values if len(values) > 0]

    if not valid_runs:
        plt.close()
        return

    # 找到最短长度并裁剪
    min_len = min(len(values) for values in valid_runs)
    all_values = [values[:min_len] for values in valid_runs]
    all_values = np.array(all_values)

    # 计算平均值和标准差
    mean_values = np.mean(all_values, axis=0)
    std_values = np.std(all_values, axis=0)

    # 创建x轴
    x = np.arange(len(mean_values))

    # 获取颜色
    colors = get_publication_colors()

    # 绘制平均值曲线
    plt.plot(x, mean_values, label=exp_name, color=colors[0], linewidth=2.5)

    # 绘制置信区间
    plt.fill_between(
        x, mean_values - std_values, mean_values + std_values,
        alpha=0.2, color=colors[0], linewidth=0,
        label='$\pm$ 1 std. dev.'
    )

    # 改进网格线
    plt.grid(True, linestyle='--', alpha=0.7)

    # 设置图表标题和轴标签
    metric_name_formatted = metric_name.replace('_', ' ').title()
    plt.title(f'{metric_title} Learning Curve', fontweight='bold')
    plt.xlabel('Episode', fontweight='bold')
    plt.ylabel(metric_title, fontweight='bold')

    # 改进图例位置和样式
    legend = plt.legend(loc='best', frameon=True, framealpha=0.9, edgecolor='gray')
    legend.get_frame().set_linewidth(0.8)

    # 调整布局
    plt.tight_layout()

    # 保存高分辨率图像
    save_path = os.path.join(save_dir, f'{metric_name}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 同时保存PDF格式用于出版
    pdf_path = os.path.join(save_dir, f'{metric_name}.pdf')
    plt.savefig(pdf_path, format='pdf', bbox_inches='tight')

    plt.close()


def plot_performance_summary(all_runs, save_dir, exp_name):
    """绘制性能摘要图"""
    # 设置绘图风格
    setup_publication_style()

    # 提取测试性能指标
    test_rewards = [run.get('test_reward', 0) for run in all_runs]
    test_extract_purities = [run.get('test_extract_purity', 0) for run in all_runs]
    test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in all_runs]
    test_productivities = [run.get('test_productivity', 0) for run in all_runs]

    # 计算平均值
    avg_reward = np.mean(test_rewards)
    avg_extract = np.mean(test_extract_purities)
    avg_raffinate = np.mean(test_raffinate_purities)
    avg_productivity = np.mean(test_productivities)

    # 计算标准差
    std_reward = np.std(test_rewards)
    std_extract = np.std(test_extract_purities)
    std_raffinate = np.std(test_raffinate_purities)
    std_productivity = np.std(test_productivities)

    # 创建性能指标条形图
    fig, ax = plt.subplots(figsize=(12, 7))

    metrics = ['Test Reward', 'Extract Purity', 'Raffinate Purity', 'Productivity x100']
    values = [avg_reward, avg_extract, avg_raffinate, avg_productivity * 100]
    errors = [std_reward, std_extract, std_raffinate, std_productivity * 100]

    x = np.arange(len(metrics))

    # 获取颜色
    colors = get_publication_colors(len(metrics))

    # 创建高质量条形图
    bars = ax.bar(
        x, values, yerr=errors, capsize=10,
        color=colors, edgecolor='black', linewidth=1.5,
        error_kw={'elinewidth': 2, 'capthick': 2}
    )

    # 添加数值标签
    for i, v in enumerate(values):
        if i == 3:  # 生产率
            ax.text(i, v + errors[i] + 0.02, f'{v:.2f}', ha='center', fontweight='bold')
        else:
            ax.text(i, v + errors[i] + 0.02, f'{v:.3f}', ha='center', fontweight='bold')

    # 设置坐标轴
    ax.set_xticks(x)
    ax.set_xticklabels(metrics, fontweight='bold')

    # 图表标题和标签
    ax.set_title(f'Performance Metrics - {exp_name}', fontweight='bold', pad=15)
    ax.set_ylabel('Value', fontweight='bold')

    # 改进网格线
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # 添加边框
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
        spine.set_visible(True)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(save_dir, 'performance_summary.png'), dpi=300, bbox_inches='tight')
    plt.savefig(os.path.join(save_dir, 'performance_summary.pdf'), format='pdf', bbox_inches='tight')

    plt.close()


def plot_pareto_analysis(results, save_dir):
    """绘制帕累托前沿分析图"""
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)

    # 设置绘图风格
    setup_publication_style()

    # 收集不同配置的性能数据
    configs = []
    extract_purities = []
    productivities = []
    weights = []

    for name, result in results.items():
        # 提取测试性能指标
        test_extract_purity = np.mean([run.get('test_extract_purity', 0) for run in result['runs']])
        test_productivity = np.mean([run.get('test_productivity', 0) for run in result['runs']])

        # 获取配置权重
        config = result['config']
        purity_weight = config.get('purity_weight', 0.5)
        productivity_weight = config.get('productivity_weight', 0.5)

        # 保存数据
        configs.append(name)
        extract_purities.append(test_extract_purity)
        productivities.append(test_productivity)
        weights.append(f"{purity_weight}/{productivity_weight}")

    # 创建高质量帕累托前沿分析图
    plt.figure(figsize=(10, 8))

    # 设置初始坐标轴范围，以便后续绘制理想区域
    plt.xlim([0.7, 1.0])
    plt.ylim([0.0, 0.01])

    # 添加理想区域(右上角，高纯度高生产率)
    plt.axvspan(0.95, 1.0, 0.005, plt.ylim()[1], alpha=0.1, color='lightgreen', label='Target Region')

    # 添加参考线
    plt.axvline(x=0.95, color='green', linestyle='--', alpha=0.7, linewidth=2, label='Target Purity (0.95)')
    plt.axhline(y=0.005, color='red', linestyle='--', alpha=0.7, linewidth=2, label='Target Productivity (0.005)')

    # 获取颜色
    colors = get_publication_colors(len(configs))

    # 绘制散点
    for i, config in enumerate(configs):
        plt.scatter(
            extract_purities[i], productivities[i],
            s=150, color=colors[i % len(colors)], edgecolor='black', linewidth=1.5,
            alpha=0.8, label=f"{config} ({weights[i]})"
        )

    # 绘制连线表示潜在帕累托前沿
    if len(extract_purities) > 1:
        # 按萃取纯度排序
        sorted_indices = np.argsort(extract_purities)
        sorted_extract = [extract_purities[i] for i in sorted_indices]
        sorted_productivity = [productivities[i] for i in sorted_indices]

        # 绘制线段 - 使用虚线以区分于参考线
        plt.plot(sorted_extract, sorted_productivity, 'b-', alpha=0.6, linewidth=2, linestyle='-.')

    # 图表标题和标签
    plt.xlabel('Extract Purity', fontweight='bold', fontsize=14)
    plt.ylabel('Productivity', fontweight='bold', fontsize=14)
    plt.title('Pareto Analysis - Different Reward Weights', fontweight='bold', fontsize=16)

    # 改进网格线
    plt.grid(True, linestyle='--', alpha=0.7)

    # 改进图例
    legend = plt.legend(loc='best', frameon=True, framealpha=0.9, edgecolor='gray')
    legend.get_frame().set_linewidth(1.0)

    # 调整坐标轴，确保所有点都可见
    margin = 0.05  # 5%的边距
    x_range = max(extract_purities) - min(extract_purities)
    y_range = max(productivities) - min(productivities)

    # 只有在有足够的数据点时调整范围
    if len(extract_purities) > 1:
        plt.xlim([min(extract_purities) - margin * x_range, max(extract_purities) + margin * x_range])
        plt.ylim([max(0, min(productivities) - margin * y_range), max(productivities) + margin * y_range])

    # 调整布局
    plt.tight_layout()

    # 添加时间戳以避免覆盖
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M")

    # 保存图表（同时保存PNG和PDF格式）
    plt.savefig(os.path.join(save_dir, f'pareto_analysis_{timestamp}.png'), dpi=300, bbox_inches='tight')
    plt.savefig(os.path.join(save_dir, f'pareto_analysis_{timestamp}.pdf'), format='pdf', bbox_inches='tight')

    # 保存原始名称（向后兼容）
    plt.savefig(os.path.join(save_dir, 'pareto_analysis.png'), dpi=300, bbox_inches='tight')

    plt.close()

    # 创建表格数据并保存
    table_data = pd.DataFrame({
        'Configuration': configs,
        'Weights (Purity/Productivity)': weights,
        'Extract Purity': extract_purities,
        'Productivity': productivities
    })

    table_data.to_csv(os.path.join(save_dir, f'pareto_analysis_{timestamp}.csv'), index=False)
    # 同时保存原始名称
    table_data.to_csv(os.path.join(save_dir, 'pareto_analysis.csv'), index=False)

    return os.path.join(save_dir, f'pareto_analysis_{timestamp}.png')


def plot_comparison_metrics(results, metrics, save_dir):
    """绘制多个实验的对比图表"""
    # 创建保存目录，添加时间戳避免覆盖
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M")
    plots_dir = os.path.join(save_dir, f"plots_{timestamp}")
    os.makedirs(plots_dir, exist_ok=True)

    # 设置绘图风格
    setup_publication_style()

    # 获取颜色方案
    colors = get_publication_colors(len(results))

    for metric in metrics:
        plt.figure(figsize=(12, 8))

        if metric in ['episode_rewards', 'extract_purities', 'raffinate_purities', 'productivities',
                      'steady_state_extract_purities', 'steady_state_raffinate_purities',
                      'steady_state_productivities', 'steady_state_rewards']:
            # 对于训练过程指标，绘制学习曲线
            _plot_learning_curve(results, metric, colors, plt)
        elif metric.startswith('test_'):
            # 对于测试指标，绘制条形图
            _plot_test_metric(results, metric, colors, plt)
        else:
            # 对于其他指标，也使用条形图
            _plot_other_metric(results, metric, colors, plt)

        # 保存图表
        plt.savefig(os.path.join(plots_dir, f'{metric}.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(plots_dir, f'{metric}.pdf'), format='pdf', bbox_inches='tight')
        plt.close()

    return plots_dir


# 内部辅助函数
def _plot_learning_curve(results, metric, colors, plt_obj):
    """绘制学习曲线"""
    legend_handles = []

    for idx, (name, exp_result) in enumerate(results.items()):
        # 收集所有运行的数据
        all_values = [run.get(metric, []) for run in exp_result['runs']]

        # 验证数据有效性
        valid_runs = [values for values in all_values if len(values) > 0]
        if not valid_runs:
            continue

        # 找到最短的长度并裁剪数据
        min_len = min(len(values) for values in valid_runs)
        all_values = [values[:min_len] for values in valid_runs]
        all_values = np.array(all_values)

        # 计算平均值和标准差
        mean_values = np.mean(all_values, axis=0)
        std_values = np.std(all_values, axis=0)

        # 创建x轴
        x = np.arange(len(mean_values))

        # 绘制平均值曲线
        line, = plt_obj.plot(
            x, mean_values,
            label=name,
            color=colors[idx % len(colors)],
            linewidth=2.5
        )

        # 绘制置信区间
        plt_obj.fill_between(
            x, mean_values - std_values, mean_values + std_values,
            alpha=0.2, color=colors[idx % len(colors)], linewidth=0
        )

        legend_handles.append(line)

    # 设置图表标题和轴标签
    if 'steady_state' in metric:
        metric_name = metric.replace('steady_state_', '').replace('_', ' ').title()
        plt_obj.title(f'Steady-State {metric_name} Learning Curves', fontweight='bold')
    else:
        metric_name = metric.replace('_', ' ').title()
        plt_obj.title(f'{metric_name} Learning Curves', fontweight='bold')

    plt_obj.xlabel('Episode', fontweight='bold')
    plt_obj.ylabel(metric_name, fontweight='bold')

    # 添加网格线
    plt_obj.grid(True, linestyle='--', alpha=0.7)

    # 添加图例
    if legend_handles:
        legend = plt_obj.legend(
            handles=legend_handles,
            loc='best',
            frameon=True,
            framealpha=0.9,
            edgecolor='gray'
        )
        legend.get_frame().set_linewidth(1.0)


def _plot_test_metric(results, metric, colors, plt_obj):
    """绘制测试指标条形图"""
    names = []
    values = []
    errors = []

    for name, exp_result in results.items():
        names.append(name)
        metric_values = [run.get(metric, 0) for run in exp_result['runs']]
        values.append(np.mean(metric_values))
        errors.append(np.std(metric_values))

    # 创建条形图
    x = np.arange(len(names))
    bars = plt_obj.bar(
        x, values, yerr=errors, capsize=10,
        color=colors[:len(names)], edgecolor='black', linewidth=1.5,
        error_kw={'elinewidth': 2, 'capthick': 2}
    )

    # 添加数值标签
    for i, v in enumerate(values):
        if 'productivity' in metric.lower():
            plt_obj.text(i, v + errors[i] + v * 0.05, f'{v:.5f}', ha='center', fontweight='bold')
        else:
            plt_obj.text(i, v + errors[i] + v * 0.05, f'{v:.3f}', ha='center', fontweight='bold')

    plt_obj.xticks(x, names, rotation=45)

    # 设置图表标题和轴标签
    metric_name = metric.replace('test_', '').replace('_', ' ').title()
    plt_obj.title(f'{metric_name} Comparison', fontweight='bold')
    plt_obj.ylabel(metric_name, fontweight='bold')

    # 添加网格线
    plt_obj.grid(axis='y', linestyle='--', alpha=0.7)

    # 调整布局
    plt_obj.tight_layout()


def _plot_other_metric(results, metric, colors, plt_obj):
    """绘制其他指标条形图"""
    names = []
    values = []
    errors = []

    for name, exp_result in results.items():
        names.append(name)
        metric_values = [run.get(metric, 0) for run in exp_result['runs']]
        values.append(np.mean(metric_values))
        errors.append(np.std(metric_values))

    # 创建条形图
    x = np.arange(len(names))
    bars = plt_obj.bar(
        x, values, yerr=errors, capsize=10,
        color=colors[:len(names)], edgecolor='black', linewidth=1.5,
        error_kw={'elinewidth': 2, 'capthick': 2}
    )

    # 添加数值标签
    for i, v in enumerate(values):
        plt_obj.text(i, v + errors[i] + 0.01, f'{v:.3f}', ha='center', fontweight='bold')

    plt_obj.xticks(x, names, rotation=45)

    # 设置图表标题和轴标签
    metric_name = metric.replace('_', ' ').title()
    plt_obj.title(f'{metric_name} Comparison', fontweight='bold')
    plt_obj.ylabel(metric_name, fontweight='bold')

    # 添加网格线
    plt_obj.grid(axis='y', linestyle='--', alpha=0.7)

    # 调整布局
    plt_obj.tight_layout()