import os
import numpy as np
import torch
import json
import time
import sys
import pandas as pd
from datetime import datetime

# 导入自定义组件
from smb_model import SMBModel
from smb_marl_env import SMBMultiAgentEnv
from smb_graph_constructor import SMBGraphConstructor
from smb_gnn_model import SMBGraphMARL
from smb_marl_trainer import SMBGraphMARLTrainer
from process_analysis import ProcessControlAnalyzer
from plotting import plot_metric, plot_performance_summary, create_timestamp_folder


class ExperimentRunner:
    """实验运行器：运行多组实验，比较不同的图构建策略和MARL算法"""

    def __init__(self, base_log_dir='experiments'):
        self.base_log_dir = base_log_dir
        os.makedirs(base_log_dir, exist_ok=True)
        self.experiment_configs = []
        self.results = {}
        self.process_analyzer = ProcessControlAnalyzer()

    def add_experiment(self, name, config):
        """添加实验配置"""
        self.experiment_configs.append({
            'name': name,
            'config': config
        })

    def run_experiments(self, num_runs=3, num_episodes=500, max_steps=500, save_interval=100, log_interval=1):
        """运行所有实验并保存日志"""
        for exp in self.experiment_configs:
            name = exp['name']
            config = exp['config']

            print(f"===== Running Experiment: {name} =====")

            # 创建实验日志目录
            exp_log_dir = os.path.join(self.base_log_dir, name)
            os.makedirs(exp_log_dir, exist_ok=True)

            # 记录实验结果
            exp_results = {
                'config': config,
                'runs': []
            }

            # 记录实验开始信息
            print(f"===== 实验开始: {name} =====")
            print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"配置: {json.dumps(config, indent=2)}")
            print(f"运行次数: {num_runs}")
            print(f"每次运行回合数: {num_episodes}")
            print(f"每回合最大步数: {max_steps}")
            print("=" * 50)

            # 运行多次实验
            for run in range(1, num_runs + 1):
                # 创建run特定的日志目录和日志文件
                run_log_dir, log_file = self._setup_run_logging(exp_log_dir, run)

                try:
                    print(f"--- Run {run}/{num_runs} ---")

                    # 设置随机种子
                    self._set_random_seed(config.get('seed', 42) + run)

                    # 创建环境、图构建器和模型
                    env, graph_constructor, model = self._create_components(config, max_steps)

                    # 创建训练器
                    trainer = self._create_trainer(env, model, graph_constructor, config, run_log_dir)

                    # 记录训练开始时间
                    start_time = time.time()

                    # 训练模型
                    print(f"开始训练: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    trainer.train(
                        num_episodes=num_episodes,
                        max_steps_per_episode=max_steps,
                        save_interval=save_interval,
                        log_interval=log_interval
                    )

                    # 记录训练结束时间
                    training_time = time.time() - start_time
                    print(f"训练完成，耗时: {training_time:.2f}秒")

                    # 测试模型
                    print(f"开始测试: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    test_reward, test_length, test_metrics = trainer.test(num_episodes=10, render=False)

                    # 进行过程控制分析
                    process_analysis_results = self._analyze_process_control(trainer, run_log_dir)

                    # 记录运行结果
                    run_result = self._collect_run_results(trainer, test_reward, test_length,
                                                           test_metrics, training_time, process_analysis_results)

                    exp_results['runs'].append(run_result)

                    # 保存运行结果
                    with open(os.path.join(run_log_dir, 'results.json'), 'w') as f:
                        json.dump(run_result, f)

                    # 打印运行结果摘要
                    self._print_run_summary(test_reward, test_metrics)

                except Exception as e:
                    print(f"运行 {run} 出错: {str(e)}")

                finally:
                    # 恢复原始输出
                    sys.stdout = sys.__stdout__
                    if log_file:
                        log_file.close()

            # 保存实验结果
            self.results[name] = exp_results

            with open(os.path.join(exp_log_dir, 'results.json'), 'w') as f:
                json.dump(exp_results, f)

            # 绘制结果图表
            self._plot_experiment_results(name, exp_results, exp_log_dir)

            print(f"实验 {name} 完成，日志已保存至 {exp_log_dir}")

    def _setup_run_logging(self, exp_log_dir, run):
        """设置运行日志"""
        run_log_dir = os.path.join(exp_log_dir, f'run_{run}')
        os.makedirs(run_log_dir, exist_ok=True)

        log_file_path = os.path.join(run_log_dir, f'run_{run}_log.txt')
        log_file = open(log_file_path, 'w', encoding='utf-8')

        # 保存原始标准输出
        original_stdout = sys.stdout

        # 创建同时输出到控制台和文件的类
        class Logger:
            def __init__(self, console, file):
                self.console = console
                self.file = file

            def write(self, message):
                self.console.write(message)
                self.file.write(message)
                self.file.flush()  # 确保即时写入

            def flush(self):
                self.console.flush()
                self.file.flush()

        # 替换标准输出
        sys.stdout = Logger(original_stdout, log_file)

        return run_log_dir, log_file

    def _set_random_seed(self, seed):
        """设置随机种子"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)

    def _create_components(self, config, max_steps):
        """创建环境、图构建器和模型"""
        # 创建SMB模型
        smb_model = SMBModel(dt=30, max_steps=max_steps)

        # 创建多智能体环境
        env = SMBMultiAgentEnv(
            smb_model=smb_model,
            agent_mode=config.get('agent_mode', 'control_based'),
            shared_reward=config.get('shared_reward', True),
            local_reward_weight=config.get('local_reward_weight', 0.3)
        )

        # 创建图构建器
        graph_constructor = SMBGraphConstructor(
            construction_strategy=config.get('graph_strategy', 'spatiotemporal'),
            temporal_window=config.get('temporal_window', 5)
        )

        # 创建模型
        node_dim = 10  # 假设节点特征维度为10
        edge_dim = 5  # 假设边特征维度为5
        model = SMBGraphMARL(
            node_dim=node_dim,
            edge_dim=edge_dim,
            hidden_dim=config.get('hidden_dim', 64),
            output_dim=config.get('output_dim', 64),
            gnn_type=config.get('gnn_type', 'gat'),
            n_agents=env.n_agents,
            action_dim=1 if config.get('agent_mode', 'control_based') == 'control_based' else 2,
            agent_mode=config.get('agent_mode', 'control_based')
        )

        return env, graph_constructor, model

    def _create_trainer(self, env, model, graph_constructor, config, log_dir):
        """创建训练器"""
        return SMBGraphMARLTrainer(
            env=env,
            model=model,
            graph_constructor=graph_constructor,
            algorithm=config.get('algorithm', 'ppo'),
            lr=config.get('lr', 3e-4),
            gamma=config.get('gamma', 0.99),
            buffer_size=config.get('buffer_size', 10000),
            batch_size=config.get('batch_size', 64),
            log_dir=log_dir,
            device=config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        )

    def _analyze_process_control(self, trainer, run_log_dir):
        """进行过程控制分析"""
        # 只有在轨迹数据有效时进行分析
        if hasattr(trainer, 'system_trajectory') and trainer.system_trajectory.get('steps'):
            # 创建分析结果目录
            analysis_dir = os.path.join(run_log_dir, 'process_analysis')
            os.makedirs(analysis_dir, exist_ok=True)

            # 设置轨迹数据
            self.process_analyzer.set_trajectory(trainer.system_trajectory)

            # 进行各项分析
            transient_results = self.process_analyzer.analyze_transient_performance(analysis_dir)
            control_results = self.process_analyzer.analyze_control_efficiency(analysis_dir)
            steady_state_results = self.process_analyzer.analyze_steady_state_performance(analysis_dir)
            disturbance_results = self.process_analyzer.analyze_disturbance_rejection(save_dir=analysis_dir)

            # 生成综合报告
            report_path = self.process_analyzer.generate_comprehensive_report(analysis_dir)

            print(f"过程控制分析完成，报告已保存到: {report_path}")

            return {
                'transient': transient_results,
                'control': control_results,
                'steady_state': steady_state_results,
                'disturbance': disturbance_results,
                'report_path': report_path
            }

        return None

    def _collect_run_results(self, trainer, test_reward, test_length, test_metrics, training_time,
                             process_analysis=None):
        """收集运行结果"""
        run_result = {
            'episode_rewards': trainer.episode_rewards,
            'episode_lengths': trainer.episode_lengths,
            'extract_purities': getattr(trainer, 'extract_purities', []),
            'raffinate_purities': getattr(trainer, 'raffinate_purities', []),
            'productivities': getattr(trainer, 'productivities', []),
            'cost_components_history': getattr(trainer, 'cost_components_history', {}),
            'total_steps': trainer.total_steps,
            'training_time': training_time,
            'test_reward': test_reward,
            'test_length': test_length,
            'test_extract_purity': test_metrics.get('extract_purity', 0),
            'test_raffinate_purity': test_metrics.get('raffinate_purity', 0),
            'test_productivity': test_metrics.get('productivity', 0),
            'test_cost_components': test_metrics.get('cost_components', {})
        }

        # 添加稳态信息
        if 'steady_state' in test_metrics:
            run_result['steady_state'] = test_metrics['steady_state']

        # 添加过程控制分析结果
        if process_analysis:
            run_result['process_control_analysis'] = {
                'transient_performance': process_analysis['transient'].get('average', {}),
                'control_efficiency': process_analysis['control'],
                'steady_state_performance': process_analysis['steady_state'],
                'disturbance_rejection': process_analysis['disturbance'].get('average', {})
            }

        return run_result

    def _print_run_summary(self, test_reward, test_metrics):
        """打印运行结果摘要"""
        print(f"\n--- 结果摘要 ---")
        print(f"测试奖励: {test_reward:.4f}")
        print(f"萃取纯度: {test_metrics['extract_purity']:.4f}")
        print(f"萃余纯度: {test_metrics['raffinate_purity']:.4f}")
        print(f"生产率: {test_metrics['productivity']:.6f}")
        print("-" * 30)

    def _plot_experiment_results(self, exp_name, exp_results, save_dir):
        """为单个实验绘制结果图表"""
        print(f"为实验 {exp_name} 绘制图表...")

        # 创建图表保存目录
        plots_dir = os.path.join(save_dir, 'experiment_summary')
        os.makedirs(plots_dir, exist_ok=True)

        # 获取所有运行的数据
        all_runs = exp_results['runs']
        if not all_runs:
            print("没有可用数据进行绘图")
            return

        # 绘制各指标曲线
        plot_metric(all_runs, 'episode_rewards', 'Episode Rewards', plots_dir, exp_name)
        plot_metric(all_runs, 'extract_purities', 'Extract Purity', plots_dir, exp_name)
        plot_metric(all_runs, 'raffinate_purities', 'Raffinate Purity', plots_dir, exp_name)
        plot_metric(all_runs, 'productivities', 'Productivity', plots_dir, exp_name)

        # 绘制综合性能指标
        plot_performance_summary(all_runs, plots_dir, exp_name)

        print(f"图表已保存至 {plots_dir}")

    def export_results_summary(self, save_dir=None):
        """导出实验结果摘要"""
        if not self.results:
            print("没有可用的实验结果")
            return None

        # 创建保存目录
        if save_dir is None:
            save_dir = os.path.join(self.base_log_dir, 'summary')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_dir = os.path.join(save_dir, f'summary_{timestamp}')
        os.makedirs(summary_dir, exist_ok=True)

        # 创建结果表格
        results_df = []

        for name, exp_result in self.results.items():
            for run_idx, run in enumerate(exp_result['runs']):
                # 基本指标
                row = {
                    'Experiment': name,
                    'Run': run_idx + 1,
                    'Graph Strategy': exp_result['config'].get('graph_strategy', ''),
                    'Algorithm': exp_result['config'].get('algorithm', ''),
                    'Agent Mode': exp_result['config'].get('agent_mode', ''),
                    'Test Reward': run.get('test_reward', 0),
                    'Test Length': run.get('test_length', 0),
                    'Extract Purity': run.get('test_extract_purity', 0),
                    'Raffinate Purity': run.get('test_raffinate_purity', 0),
                    'Productivity': run.get('test_productivity', 0),
                    'Training Time': run.get('training_time', 0),
                    'Total Steps': run.get('total_steps', 0)
                }

                # 添加稳态性能指标
                if 'steady_state' in run:
                    row.update({
                        'Steady State Step': run['steady_state'].get('step', 0),
                        'Steady Extract Purity': run['steady_state'].get('extract_purity', 0),
                        'Steady Raffinate Purity': run['steady_state'].get('raffinate_purity', 0),
                        'Steady Productivity': run['steady_state'].get('productivity', 0),
                        'Steady Reward': run['steady_state'].get('reward', 0)
                    })

                # 添加过程控制分析指标
                if 'process_control_analysis' in run:
                    pca = run['process_control_analysis']
                    if 'transient_performance' in pca:
                        row.update({
                            'Rise Time': pca['transient_performance'].get('rise_time', 0),
                            'Settling Time': pca['transient_performance'].get('settling_time', 0),
                            'Overshoot': pca['transient_performance'].get('overshoot', 0)
                        })
                    if 'control_efficiency' in pca:
                        row.update({
                            'Control Change Frequency': pca['control_efficiency'].get('change_frequency', 0),
                            'Control Energy': pca['control_efficiency'].get('control_energy', 0),
                            'Control Stability': pca['control_efficiency'].get('control_stability', 0)
                        })
                    if 'disturbance_rejection' in pca:
                        row.update({
                            'Disturbance Rejection Rate': pca['disturbance_rejection'].get('rejection_rate', 0),
                            'Recovery Time': pca['disturbance_rejection'].get('recovery_time', 0)
                        })

                results_df.append(row)

        # 转换为DataFrame
        results_df = pd.DataFrame(results_df)

        # 保存详细结果表
        csv_path = os.path.join(summary_dir, 'detailed_results.csv')
        results_df.to_csv(csv_path, index=False)

        # 创建聚合表格
        columns_to_agg = [
            'Test Reward', 'Test Length', 'Extract Purity', 'Raffinate Purity', 'Productivity',
            'Training Time', 'Total Steps'
        ]

        # 添加稳态列（如果存在）
        if 'Steady State Step' in results_df.columns:
            columns_to_agg.extend([
                'Steady State Step', 'Steady Extract Purity', 'Steady Raffinate Purity',
                'Steady Productivity', 'Steady Reward'
            ])

        # 添加过程控制分析列（如果存在）
        process_control_cols = ['Rise Time', 'Settling Time', 'Overshoot',
                                'Control Change Frequency', 'Control Energy', 'Control Stability',
                                'Disturbance Rejection Rate', 'Recovery Time']
        for col in process_control_cols:
            if col in results_df.columns:
                columns_to_agg.append(col)

        # 按实验分组聚合
        agg_df = results_df.groupby(['Experiment', 'Graph Strategy', 'Algorithm', 'Agent Mode'])[
            columns_to_agg].agg(['mean', 'std']).reset_index()

        # 修复列名
        agg_df.columns = ['_'.join(col).strip('_') for col in agg_df.columns.values]

        # 保存聚合表格
        agg_path = os.path.join(summary_dir, 'aggregate_results.csv')
        agg_df.to_csv(agg_path, index=False)

        # 创建易读格式的结果摘要
        summary_path = os.path.join(summary_dir, 'results_summary.txt')
        with open(summary_path, 'w') as f:
            f.write(f"结果摘要 (生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})\n")
            f.write("=" * 80 + "\n\n")

            for name, exp_result in self.results.items():
                f.write(f"实验: {name}\n")
                f.write("-" * 50 + "\n")

                # 配置信息
                f.write("配置:\n")
                for k, v in exp_result['config'].items():
                    f.write(f"  {k}: {v}\n")
                f.write("\n")

                # 性能指标
                test_metrics = [
                    ('测试奖励', 'test_reward'),
                    ('萃取纯度', 'test_extract_purity'),
                    ('萃余纯度', 'test_raffinate_purity'),
                    ('生产率', 'test_productivity')
                ]

                f.write("性能指标:\n")
                for label, key in test_metrics:
                    values = [run.get(key, 0) for run in exp_result['runs']]
                    f.write(f"  {label}: {np.mean(values):.4f} ± {np.std(values):.4f}\n")

                # 稳态性能指标
                has_steady_state = any('steady_state' in run for run in exp_result['runs'])
                if has_steady_state:
                    f.write("\n稳态性能指标:\n")

                    # 稳态步骤
                    steps = [run['steady_state'].get('step', 0) for run in exp_result['runs'] if
                             'steady_state' in run]
                    if steps:
                        f.write(f"  稳态达成时间: {np.mean(steps):.1f} ± {np.std(steps):.1f} 步\n")

                    # 稳态性能指标
                    steady_metrics = [
                        ('萃取纯度', 'extract_purity'),
                        ('萃余纯度', 'raffinate_purity'),
                        ('生产率', 'productivity'),
                        ('奖励', 'reward')
                    ]

                    for label, key in steady_metrics:
                        values = [run['steady_state'].get(key, 0) for run in exp_result['runs'] if
                                  'steady_state' in run]
                        if values:
                            f.write(f"  稳态{label}: {np.mean(values):.4f} ± {np.std(values):.4f}\n")

                # 添加过程控制分析摘要
                has_process_analysis = any('process_control_analysis' in run for run in exp_result['runs'])
                if has_process_analysis:
                    f.write("\n过程控制分析:\n")

                    # 暂态性能
                    transient_data = [run['process_control_analysis'].get('transient_performance', {})
                                      for run in exp_result['runs'] if 'process_control_analysis' in run]
                    if transient_data:
                        rise_times = [d.get('rise_time', 0) for d in transient_data if 'rise_time' in d]
                        settling_times = [d.get('settling_time', 0) for d in transient_data if 'settling_time' in d]
                        overshoots = [d.get('overshoot', 0) for d in transient_data if 'overshoot' in d]

                        if rise_times:
                            f.write(f"  上升时间: {np.mean(rise_times):.2f} ± {np.std(rise_times):.2f} 步\n")
                        if settling_times:
                            f.write(f"  稳定时间: {np.mean(settling_times):.2f} ± {np.std(settling_times):.2f} 步\n")
                        if overshoots:
                            f.write(f"  超调量: {np.mean(overshoots) * 100:.2f}% ± {np.std(overshoots) * 100:.2f}%\n")

                    # 控制效率
                    control_data = [run['process_control_analysis'].get('control_efficiency', {})
                                    for run in exp_result['runs'] if 'process_control_analysis' in run]
                    if control_data:
                        frequencies = [d.get('change_frequency', 0) for d in control_data if 'change_frequency' in d]
                        energies = [d.get('control_energy', 0) for d in control_data if 'control_energy' in d]
                        stabilities = [d.get('control_stability', 0) for d in control_data if 'control_stability' in d]

                        if frequencies:
                            f.write(f"  控制动作频率: {np.mean(frequencies):.4f} ± {np.std(frequencies):.4f} 变化/步\n")
                        if energies:
                            f.write(f"  控制能耗指标: {np.mean(energies):.6f} ± {np.std(energies):.6f}\n")
                        if stabilities:
                            f.write(f"  控制稳定性指标: {np.mean(stabilities):.6f} ± {np.std(stabilities):.6f}\n")

                    # 扰动抑制
                    disturbance_data = [run['process_control_analysis'].get('disturbance_rejection', {})
                                        for run in exp_result['runs'] if 'process_control_analysis' in run]
                    if disturbance_data:
                        rejection_rates = [d.get('rejection_rate', 0) for d in disturbance_data if
                                           'rejection_rate' in d]
                        recovery_times = [d.get('recovery_time', 0) for d in disturbance_data if 'recovery_time' in d]

                        if rejection_rates:
                            f.write(f"  扰动抑制率: {np.mean(rejection_rates):.4f} ± {np.std(rejection_rates):.4f}\n")
                        if recovery_times:
                            f.write(
                                f"  平均恢复时间: {np.mean(recovery_times):.2f} ± {np.std(recovery_times):.2f} 步\n")

                f.write("\n" + "=" * 50 + "\n\n")

        print(f"结果摘要已保存至: {summary_dir}")

        return {
            'detailed_results': csv_path,
            'aggregate_results': agg_path,
            'summary': summary_path,
            'directory': summary_dir
        }