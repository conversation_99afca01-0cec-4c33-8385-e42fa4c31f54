import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, GCNConv, GINConv, global_mean_pool, global_add_pool, AttentionalAggregation


class SMBGraphEncoder(nn.Module):
    """
    SMB图编码器

    使用图神经网络提取SMB系统图结构的特征
    支持多种GNN架构和注意力机制

    参数:
    -----
    node_dim: int
        节点特征维度
    edge_dim: int
        边特征维度
    hidden_dim: int
        隐藏层维度
    output_dim: int
        输出特征维度
    num_layers: int
        GNN层数
    gnn_type: str
        GNN类型: 'gat' (图注意力网络), 'gcn' (图卷积网络), 'gin' (图同构网络)
    use_edge_attr: bool
        是否使用边特征
    dropout: float
        Dropout率
    """

    def __init__(self,
                 node_dim,
                 edge_dim=0,
                 hidden_dim=64,
                 output_dim=64,
                 num_layers=3,
                 gnn_type='gat',
                 use_edge_attr=True,
                 dropout=0.1):
        super(SMBGraphEncoder, self).__init__()

        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.gnn_type = gnn_type
        self.use_edge_attr = use_edge_attr

        # 节点特征编码器
        self.node_encoder = nn.Sequential(
            nn.Linear(node_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout)
        )

        # 边特征编码器(如果使用边特征)
        if use_edge_attr and edge_dim > 0:
            self.edge_encoder = nn.Sequential(
                nn.Linear(edge_dim, hidden_dim),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim),
                nn.Dropout(dropout)
            )

        # GNN层
        self.gnn_layers = nn.ModuleList()

        # 第一层
        if gnn_type == 'gat':
            self.gnn_layers.append(
                GATConv(hidden_dim, hidden_dim, heads=4, dropout=dropout,
                        edge_dim=hidden_dim if use_edge_attr else None)
            )
        elif gnn_type == 'gcn':
            self.gnn_layers.append(
                GCNConv(hidden_dim, hidden_dim, improved=True)
            )
        elif gnn_type == 'gin':
            nn_layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim),
                nn.Linear(hidden_dim, hidden_dim)
            )
            self.gnn_layers.append(
                GINConv(nn_layer)
            )

        # 后续层
        for i in range(1, num_layers):
            if gnn_type == 'gat':
                # 如果是GAT，第一层使用多头注意力，输出维度是hidden_dim*heads
                in_channels = hidden_dim * 4 if i == 1 else hidden_dim
                self.gnn_layers.append(
                    GATConv(in_channels, hidden_dim, dropout=dropout,
                            edge_dim=hidden_dim if use_edge_attr else None)
                )
            elif gnn_type == 'gcn':
                self.gnn_layers.append(
                    GCNConv(hidden_dim, hidden_dim, improved=True)
                )
            elif gnn_type == 'gin':
                nn_layer = nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.LayerNorm(hidden_dim),
                    nn.Linear(hidden_dim, hidden_dim)
                )
                self.gnn_layers.append(
                    GINConv(nn_layer)
                )

        # 全局池化层
        gate_nn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        self.global_attention_pool = AttentionalAggregation(gate_nn)

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )

        # 节点类型嵌入
        self.node_type_embedding = nn.Embedding(10, hidden_dim)  # 假设最多10种节点类型
        self.edge_type_embedding = nn.Embedding(10, hidden_dim)  # 假设最多10种边类型

    def forward(self, data):
        """
        前向传播

        参数:
        -----
        data: torch_geometric.data.Data
            PyTorch Geometric格式的图数据

        返回:
        -----
        x: torch.Tensor
            图级别的嵌入 [batch_size, output_dim]
        node_embeddings: torch.Tensor
            节点级别的嵌入 [num_nodes, hidden_dim]
        """
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr

        # 动态创建特征适配层
        if not hasattr(self, 'feature_adapter') or self.feature_adapter.in_features != x.size(1):
            self.feature_adapter = nn.Linear(x.size(1), self.node_dim).to(x.device)

        # 使用特征适配层
        x = F.relu(self.feature_adapter(x))

        # 节点类型嵌入
        if hasattr(data, 'node_type'):
            node_type_emb = self.node_type_embedding(data.node_type)
            x = torch.cat([self.node_encoder(x), node_type_emb], dim=1)

            # 动态创建调整层
            if not hasattr(self, 'node_adjust') or self.node_adjust.in_features != x.size(1):
                self.node_adjust = nn.Linear(x.size(1), self.hidden_dim).to(x.device)

            x = self.node_adjust(x)
        else:
            x = self.node_encoder(x)

        # 边特征处理
        if self.use_edge_attr and edge_attr is not None:
            # 动态创建边特征适配层
            if not hasattr(self, 'edge_adapter') or self.edge_adapter.in_features != edge_attr.size(1):
                self.edge_adapter = nn.Linear(edge_attr.size(1), self.edge_dim).to(edge_attr.device)

            edge_attr = F.relu(self.edge_adapter(edge_attr))

            if hasattr(data, 'edge_type'):
                edge_type_emb = self.edge_type_embedding(data.edge_type)
                edge_attr = torch.cat([self.edge_encoder(edge_attr), edge_type_emb], dim=1)

                # 动态创建调整层
                if not hasattr(self, 'edge_adjust') or self.edge_adjust.in_features != edge_attr.size(1):
                    self.edge_adjust = nn.Linear(edge_attr.size(1), self.hidden_dim).to(edge_attr.device)

                edge_attr = self.edge_adjust(edge_attr)
            else:
                edge_attr = self.edge_encoder(edge_attr)

        # GNN层
        for i, gnn_layer in enumerate(self.gnn_layers):
            if self.gnn_type == 'gat' and self.use_edge_attr and edge_attr is not None:
                x = gnn_layer(x, edge_index, edge_attr=edge_attr)
            else:
                x = gnn_layer(x, edge_index)

            # 除了最后一层外，应用ReLU和Dropout
            if i < len(self.gnn_layers) - 1:
                x = F.relu(x)
                x = F.dropout(x, p=0.1, training=self.training)

        # 保存节点嵌入
        node_embeddings = x

        # 全局池化
        if hasattr(data, 'batch'):
            x = self.global_attention_pool(x, data.batch)
        else:
            # 如果没有batch信息，假设只有一个图
            x = self.global_attention_pool(x, torch.zeros(x.size(0), dtype=torch.long, device=x.device))

        # 输出层
        x = self.output_layer(x)

        return x, node_embeddings


class SMBGraphMARL(nn.Module):
    """
    基于图的多智能体强化学习模型

    使用图神经网络提取特征，为每个智能体生成策略

    参数:
    -----
    node_dim: int
        节点特征维度
    edge_dim: int
        边特征维度
    hidden_dim: int
        隐藏层维度
    output_dim: int
        输出特征维度
    gnn_type: str
        GNN类型: 'gat' (图注意力网络), 'gcn' (图卷积网络), 'gin' (图同构网络)
    n_agents: int
        智能体数量
    action_dim: int
        每个智能体的动作维度
    agent_mode: str
        智能体模式: 'control_based' 或 'function_based'
    """

    def __init__(self,
                 node_dim,
                 edge_dim=0,
                 hidden_dim=64,
                 output_dim=64,
                 gnn_type='gat',
                 n_agents=4,
                 action_dim=1,
                 agent_mode='control_based'):
        super(SMBGraphMARL, self).__init__()

        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.n_agents = n_agents
        self.action_dim = action_dim
        self.agent_mode = agent_mode

        # 图编码器
        self.graph_encoder = SMBGraphEncoder(
            node_dim=node_dim,
            edge_dim=edge_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            gnn_type=gnn_type,
            use_edge_attr=True
        )

        # 智能体掩码生成器
        self.agent_mask_generator = AgentMaskGenerator(agent_mode=agent_mode)

        # 每个智能体的策略网络
        self.policy_networks = nn.ModuleList()
        for _ in range(n_agents):
            self.policy_networks.append(
                nn.Sequential(
                    nn.Linear(output_dim, hidden_dim),
                    nn.ReLU(),
                    nn.LayerNorm(hidden_dim),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.LayerNorm(hidden_dim),
                    nn.Linear(hidden_dim, action_dim)
                )
            )

        # 每个智能体的价值网络 (用于Actor-Critic方法)
        self.value_networks = nn.ModuleList()
        for _ in range(n_agents):
            self.value_networks.append(
                nn.Sequential(
                    nn.Linear(output_dim, hidden_dim),
                    nn.ReLU(),
                    nn.LayerNorm(hidden_dim),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.LayerNorm(hidden_dim),
                    nn.Linear(hidden_dim, 1)
                )
            )

    def forward(self, graph_data, agent_ids=None):
        """
        前向传播

        参数:
        -----
        graph_data: torch_geometric.data.Data
            PyTorch Geometric格式的图数据
        agent_ids: list, optional
            需要计算策略的智能体ID列表，默认为所有智能体

        返回:
        -----
        actions: dict
            每个智能体的动作分布
        values: dict
            每个智能体的状态价值
        """
        # 如果未指定智能体ID，则使用所有智能体
        if agent_ids is None:
            if self.agent_mode == 'control_based':
                agent_ids = [f'agent_{i}' for i in range(self.n_agents)]
            else:  # function_based
                agent_ids = ['extract_agent', 'raffinate_agent']

        # 从图中提取特征
        graph_embedding, node_embeddings = self.graph_encoder(graph_data)

        # 为每个智能体生成掩码
        agent_masks = self.agent_mask_generator.generate_masks(graph_data)

        # 为每个智能体计算特征
        actions = {}
        values = {}

        for i, agent_id in enumerate(agent_ids):
            if i >= len(self.policy_networks):
                continue

            # 确定掩码
            if agent_id in agent_masks:
                mask = agent_masks[agent_id]
            else:
                # 默认掩码 - 使用所有节点
                mask = torch.ones(node_embeddings.size(0), dtype=torch.bool, device=node_embeddings.device)

            # 选择相关节点的嵌入
            if mask.sum() > 0:
                # 提取与该智能体相关的节点嵌入
                agent_nodes = node_embeddings[mask]

                # 全局池化获取智能体特征
                # 如果只有一个相关节点，直接使用它的嵌入
                if agent_nodes.size(0) == 1:
                    agent_feature = agent_nodes.squeeze(0)
                else:
                    # 否则使用平均池化
                    agent_feature = agent_nodes.mean(dim=0)
            else:
                # 如果没有相关节点，使用全局图嵌入
                agent_feature = graph_embedding

            # 计算动作和价值
            action = self.policy_networks[i](agent_feature)
            value = self.value_networks[i](agent_feature)

            # 存储结果
            actions[agent_id] = action
            values[agent_id] = value

        return actions, values


class AgentMaskGenerator:
    """
    智能体掩码生成器

    根据智能体模式和图结构生成智能体关注的节点掩码

    参数:
    -----
    agent_mode: str
        智能体模式: 'control_based' 或 'function_based'
    """

    def __init__(self, agent_mode='control_based'):
        self.agent_mode = agent_mode

    def generate_masks(self, graph_data):
        """
        生成智能体掩码

        参数:
        -----
        graph_data: torch_geometric.data.Data
            PyTorch Geometric格式的图数据

        返回:
        -----
        masks: dict
            每个智能体的节点掩码
        """
        masks = {}

        # 获取节点名称列表(如果有)
        if not hasattr(graph_data, 'node_names'):
            # 如果没有节点名称，无法确定掩码
            return masks

        node_names = graph_data.node_names

        # 为每个智能体生成掩码
        if self.agent_mode == 'control_based':
            # 控制模式: 4个智能体，每个控制2列
            for i in range(4):
                agent_id = f'agent_{i}'

                # 初始化掩码
                mask = torch.zeros(len(node_names), dtype=torch.bool)

                # 找出智能体在图中的索引(如果存在)
                agent_idx = None
                for j, name in enumerate(node_names):
                    if name == agent_id:
                        agent_idx = j
                        mask[j] = True  # 包含智能体自身
                        break

                # 找出智能体控制的列
                primary_columns = [i * 2, i * 2 + 1]
                for j, name in enumerate(node_names):
                    if name.startswith('column_'):
                        col_idx = int(name.split('_')[1])
                        if col_idx in primary_columns:
                            mask[j] = True  # 包含主要控制的列

                # 找出与该区段相关的端口
                # 区段1(0,1)控制脱附剂和萃取口
                # 区段2(2,3)控制进料和萃余口
                if i == 0:
                    for j, name in enumerate(node_names):
                        if name in ['desorbent', 'extract', 'extract_purity']:
                            mask[j] = True
                elif i == 1:
                    for j, name in enumerate(node_names):
                        if name in ['extract', 'extract_purity']:
                            mask[j] = True
                elif i == 2:
                    for j, name in enumerate(node_names):
                        if name in ['feed', 'raffinate', 'raffinate_purity']:
                            mask[j] = True
                elif i == 3:
                    for j, name in enumerate(node_names):
                        if name in ['feed', 'raffinate', 'raffinate_purity']:
                            mask[j] = True

                # 存储掩码
                masks[agent_id] = mask

        else:  # function_based
            # 功能模式: 2个智能体，分别控制萃取和萃余

            # 萃取智能体掩码
            extract_mask = torch.zeros(len(node_names), dtype=torch.bool)

            # 找出萃取智能体在图中的索引(如果存在)
            for j, name in enumerate(node_names):
                if name == 'extract_agent':
                    extract_mask[j] = True  # 包含智能体自身
                    break

            # 找出萃取智能体关注的列(区段1和2: 0-3)
            for j, name in enumerate(node_names):
                if name.startswith('column_'):
                    col_idx = int(name.split('_')[1])
                    if col_idx < 4:  # 列0-3
                        extract_mask[j] = True

            # 找出与萃取相关的端口和性能指标
            for j, name in enumerate(node_names):
                if name in ['desorbent', 'extract', 'extract_purity']:
                    extract_mask[j] = True

            # 萃余智能体掩码
            raffinate_mask = torch.zeros(len(node_names), dtype=torch.bool)

            # 找出萃余智能体在图中的索引(如果存在)
            for j, name in enumerate(node_names):
                if name == 'raffinate_agent':
                    raffinate_mask[j] = True  # 包含智能体自身
                    break

            # 找出萃余智能体关注的列(区段3和4: 4-7)
            for j, name in enumerate(node_names):
                if name.startswith('column_'):
                    col_idx = int(name.split('_')[1])
                    if col_idx >= 4:  # 列4-7
                        raffinate_mask[j] = True

            # 找出与萃余相关的端口和性能指标
            for j, name in enumerate(node_names):
                if name in ['feed', 'raffinate', 'raffinate_purity']:
                    raffinate_mask[j] = True

            # 存储掩码
            masks['extract_agent'] = extract_mask
            masks['raffinate_agent'] = raffinate_mask

        return masks