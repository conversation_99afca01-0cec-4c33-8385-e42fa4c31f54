import gym
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from collections import deque
import random
import os
import time

from matplotlib import pyplot as plt
from matplotlib import cm
from torch.utils.tensorboard import SummaryWriter


class ReplayBuffer:
    """
    经验回放缓冲区

    存储多智能体环境的经验，支持批量采样

    参数:
    -----
    capacity: int
        缓冲区容量
    """

    def __init__(self, capacity=10000):
        self.buffer = deque(maxlen=capacity)

    def add(self, state, actions, rewards, next_state, dones):
        """
        添加经验

        参数:
        -----
        state: dict
            当前状态
        actions: dict
            每个智能体的动作
        rewards: dict
            每个智能体的奖励
        next_state: dict
            下一个状态
        dones: dict
            每个智能体的完成标志
        """
        self.buffer.append((state, actions, rewards, next_state, dones))

    def sample(self, batch_size):
        """
        采样经验批次

        参数:
        -----
        batch_size: int
            批次大小

        返回:
        -----
        batch: tuple
            经验批次(状态，动作，奖励，下一个状态，完成标志)
        """
        batch = random.sample(self.buffer, min(len(self.buffer), batch_size))
        states, actions, rewards, next_states, dones = zip(*batch)
        return states, actions, rewards, next_states, dones

    def __len__(self):
        return len(self.buffer)

    def can_sample(self, batch_size=64):
        """检查是否可以采样"""
        return len(self.buffer) >= batch_size


class SMBGraphMARLTrainer:
    """
    基于图的多智能体强化学习训练器

    训练SMBGraphMARL模型，实现基于图注意力的协作控制
    支持多种RL算法，包括DQN, A2C, PPO

    参数:
    -----
    env: SMBMultiAgentEnv
        SMB多智能体环境
    model: SMBGraphMARL
        基于图的多智能体强化学习模型
    graph_constructor: SMBGraphConstructor
        图构建器
    algorithm: str
        强化学习算法: 'dqn', 'a2c', 'ppo'
    lr: float
        学习率
    gamma: float
        折扣因子
    buffer_size: int
        经验回放缓冲区大小
    batch_size: int
        批次大小
    update_target_every: int
        目标网络更新频率(仅用于DQN)
    clip_grad: float
        梯度裁剪阈值
    log_dir: str
        TensorBoard日志目录
    device: str
        训练设备: 'cpu' 或 'cuda'
    """

    def __init__(self,
                 env,
                 model,
                 graph_constructor,
                 algorithm='ppo',
                 lr=3e-4,
                 gamma=0.99,
                 buffer_size=5000,
                 batch_size=64,
                 update_target_every=100,
                 clip_grad=1.0,
                 ppo_epsilon=0.2,
                 entropy_coef=0.01,
                 value_coef=0.5,
                 log_dir='logs',
                 device='cuda',
                 purity_weight=1.0,
                 productivity_weight=1.0):
        self.env = env
        self.model = model
        self.graph_constructor = graph_constructor
        self.algorithm = algorithm
        self.lr = lr
        self.gamma = gamma
        self.batch_size = batch_size
        self.update_target_every = update_target_every
        self.clip_grad = clip_grad
        self.ppo_epsilon = ppo_epsilon
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.log_dir = log_dir
        self.device = device

        # 将模型移动到指定设备
        self.model = self.model.to(device)

        # 如果使用DQN，创建目标网络
        if algorithm == 'dqn':
            self.target_model = type(model)(
                node_dim=model.node_dim,
                edge_dim=model.edge_dim,
                hidden_dim=model.hidden_dim,
                output_dim=model.output_dim,
                gnn_type=model.graph_encoder.gnn_type,
                n_agents=model.n_agents,
                action_dim=model.action_dim,
                agent_mode=model.agent_mode
            ).to(device)
            self.target_model.load_state_dict(self.model.state_dict())
            self.target_model.eval()

        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)

        # 经验回放缓冲区
        self.replay_buffer = ReplayBuffer(capacity=buffer_size)

        # PPO缓冲区
        if algorithm == 'ppo':
            self.ppo_buffer = {
                'states': [],
                'actions': [],
                'action_probs': [],
                'values': [],
                'rewards': [],
                'dones': []
            }

        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        self.log_dir = log_dir

        # TensorBoard日志
        self.writer = SummaryWriter(log_dir=log_dir)

        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.total_steps = 0
        self.episodes = 0
        self.updates = 0

        # 性能指标记录
        self.extract_purities = []
        self.raffinate_purities = []
        self.productivities = []
        self.cost_components_history = {
            'base_cost': [],
            'productivity_term': [],
            'penalty_extract_term': [],
            'penalty_raffinate_term': [],
            'total_cost': []
        }

        # 稳态性能评估相关变量
        self.steady_state_rewards = []
        self.steady_state_extract_purities = []
        self.steady_state_raffinate_purities = []
        self.steady_state_productivities = []
        self.steady_state_costs = []

        # 稳态检测参数
        self.steady_state_window = 50  # 稳态判断窗口大小
        self.steady_state_threshold = 0.01  # 稳态判断阈值

        # 添加系统响应跟踪
        self.system_trajectory = {
            'steps': [],
            'extract_purity': [],
            'raffinate_purity': [],
            'productivity': [],
            'control_inputs': [],
            'reward': []
        }
        self.test_extract_purities = []
        self.test_productivities = []

        # 保存奖励权重参数
        self.purity_weight = purity_weight
        self.productivity_weight = productivity_weight

        # 设置环境中的奖励权重
        if hasattr(env, 'env') and hasattr(env.env, 'set_reward_weights'):
            env.env.set_reward_weights(purity_weight, productivity_weight)

    def select_action(self, graph_data, epsilon=0.0):
        """
        选择动作

        参数:
        -----
        graph_data: torch_geometric.data.Data
            图数据
        epsilon: float
            探索率(仅用于DQN)

        返回:
        -----
        actions: dict
            每个智能体的动作
        action_info: dict
            额外的动作信息(如动作概率、状态价值等)
        """
        # 将图数据移动到指定设备
        graph_data = graph_data.to(self.device)

        # 计算每个智能体的动作和价值
        self.model.eval()
        with torch.no_grad():
            actions_tensor, values_tensor = self.model(graph_data)
        self.model.train()

        # 转换为Python字典
        actions = {}
        values = {}
        action_probs = {}

        for agent_id, action_tensor in actions_tensor.items():
            if self.algorithm == 'dqn':
                # DQN: 离散动作
                if random.random() < epsilon:
                    # 探索: 随机动作
                    if isinstance(self.env.action_spaces[agent_id], gym.spaces.Box):
                        action = np.random.uniform(
                            self.env.action_spaces[agent_id].low,
                            self.env.action_spaces[agent_id].high
                        )
                    else:
                        action = np.random.randint(0, self.model.action_dim)
                else:
                    # 利用: 选择Q值最大的动作
                    action = action_tensor.argmax().item()

                actions[agent_id] = action
                action_probs[agent_id] = torch.zeros_like(action_tensor)
                action_probs[agent_id][action] = 1.0
                # 确保移动到CPU
                action_probs[agent_id] = action_probs[agent_id].cpu()

            else:  # a2c, ppo: 连续动作
                # 根据动作的类型进行处理
                if isinstance(self.env.action_spaces[agent_id], gym.spaces.Box):
                    # 连续动作: 使用高斯分布
                    mean = action_tensor
                    # 固定的标准差
                    std = torch.ones_like(mean) * 0.1

                    # 创建正态分布
                    normal_dist = torch.distributions.Normal(mean, std)

                    # 采样动作
                    action = normal_dist.sample()

                    # 裁剪到[-1, 1]范围
                    action = torch.clamp(action, -1, 1)

                    # 计算动作概率密度
                    action_prob = normal_dist.log_prob(action).exp()

                    # 转换为NumPy数组
                    action = action.cpu().numpy()
                    # 确保移动到CPU
                    action_prob = action_prob.cpu().detach()
                else:
                    # 离散动作: 使用Softmax
                    action_probs_tensor = F.softmax(action_tensor, dim=-1)

                    # 采样动作
                    action = torch.multinomial(action_probs_tensor, 1).item()

                    # 记录动作概率
                    action_prob = action_probs_tensor[action].item()
                    # 对于标量，不需要移动到CPU

                actions[agent_id] = action
                action_probs[agent_id] = action_prob

            # 记录状态价值
            values[agent_id] = values_tensor[agent_id].item()  # 转换为Python标量

        # 返回动作和额外信息
        action_info = {
            'values': values,
            'action_probs': action_probs
        }

        return actions, action_info

    def train_step_dqn(self):
        """执行一步DQN训练"""
        if not self.replay_buffer.can_sample(self.batch_size):
            return 0.0

        # 从经验回放缓冲区采样
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)

        # 将图数据批次转换为PyTorch格式
        graph_batch = []
        next_graph_batch = []

        for state, next_state in zip(states, next_states):
            graph = self.graph_constructor.construct_graph(state)
            next_graph = self.graph_constructor.construct_graph(next_state)
            graph_batch.append(graph)
            next_graph_batch.append(next_graph)

        total_loss = 0.0

        # 为每个智能体分别计算损失
        for agent_id in self.env.agent_ids:
            # 收集当前智能体的Q值和目标Q值
            curr_q_values = []
            target_q_values = []

            for i, (graph, action_dict, next_graph, reward_dict, done_dict) in enumerate(
                    zip(graph_batch, actions, next_graph_batch, rewards, dones)):

                # 跳过该智能体不存在的样本
                if agent_id not in action_dict or agent_id not in reward_dict or agent_id not in done_dict:
                    continue

                # 将图数据移动到设备
                graph = graph.to(self.device)
                next_graph = next_graph.to(self.device)

                # 获取动作
                action = action_dict[agent_id]

                # 计算当前Q值
                q_tensors, _ = self.model(graph)

                # 如果该智能体没有Q值，跳过
                if agent_id not in q_tensors:
                    continue

                q_tensor = q_tensors[agent_id]

                # 根据动作类型获取特定动作的Q值
                if isinstance(action, np.ndarray):
                    # 连续动作，直接使用整个Q值向量
                    current_q = q_tensor.reshape(-1)  # 确保是向量形式
                else:
                    # 离散动作，选择对应动作的Q值
                    if action < q_tensor.size(0):
                        current_q = q_tensor[action].reshape(-1)  # 转换为向量形式
                    else:
                        continue

                # 计算目标Q值
                with torch.no_grad():
                    next_q_tensors, _ = self.target_model(next_graph)

                    if agent_id not in next_q_tensors:
                        continue

                    next_q_tensor = next_q_tensors[agent_id]
                    max_next_q = next_q_tensor.max().item()

                    reward = reward_dict[agent_id]
                    done = done_dict[agent_id]
                    target_q = reward + (1 - done) * self.gamma * max_next_q

                # 保存Q值和目标Q值
                curr_q_values.append(current_q)
                target_q_values.append(target_q)

            # 如果没有收集到有效样本，跳过该智能体
            if not curr_q_values or not target_q_values:
                continue

            # 将收集到的Q值转换为张量
            q_tensor = torch.cat(curr_q_values)
            target_q_tensor = torch.tensor(target_q_values, dtype=torch.float32, device=self.device)

            # 计算损失
            agent_loss = F.mse_loss(q_tensor, target_q_tensor)
            total_loss += agent_loss

        # 如果没有有效的损失，返回0
        if total_loss == 0.0:
            return 0.0

        # 反向传播和优化
        self.optimizer.zero_grad()
        total_loss.backward()

        # 梯度裁剪
        if self.clip_grad > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.clip_grad)

        self.optimizer.step()

        # 更新目标网络
        self.updates += 1
        if self.updates % self.update_target_every == 0:
            self.target_model.load_state_dict(self.model.state_dict())

        return total_loss.item()

    def train_step_a2c(self, states, actions, rewards, next_states, dones, action_probs, values):
        """
        执行一步A2C训练

        参数:
        -----
        states: list
            状态列表
        actions: list
            动作列表
        rewards: list
            奖励列表
        next_states: list
            下一个状态列表
        dones: list
            完成标志列表
        action_probs: list
            动作概率列表
        values: list
            状态价值列表
        """
        # 将图数据转换为PyTorch格式
        graph_batch = []
        next_graph_batch = []

        for state, next_state in zip(states, next_states):
            # 使用图构建器将状态转换为图
            graph = self.graph_constructor.construct_graph(state)
            next_graph = self.graph_constructor.construct_graph(next_state)

            # 添加到批次
            graph_batch.append(graph)
            next_graph_batch.append(next_graph)

        # 计算优势值
        advantages = {}
        returns = {}

        # 初始化
        for agent_id in self.env.agent_ids:
            advantages[agent_id] = []
            returns[agent_id] = []

        # 计算每个智能体的优势值和回报
        with torch.no_grad():
            for i, (next_graph, reward_dict, done_dict, value_dict) in enumerate(
                    zip(next_graph_batch, rewards, dones, values)):
                # 将图数据移动到指定设备
                next_graph = next_graph.to(self.device)

                # 计算下一个状态的价值
                _, next_values_tensor = self.model(next_graph)

                # 计算每个智能体的优势值
                for agent_id, value in value_dict.items():
                    if agent_id in reward_dict and agent_id in done_dict and agent_id in next_values_tensor:
                        reward = reward_dict[agent_id]
                        done = done_dict[agent_id]
                        next_value = next_values_tensor[agent_id].item() if not done else 0.0

                        # 计算时序差分(TD)目标
                        td_target = reward + self.gamma * next_value

                        # 计算优势值
                        advantage = td_target - value

                        # 记录优势值和回报
                        advantages[agent_id].append(advantage)
                        returns[agent_id].append(td_target)

        # 计算策略和价值损失
        policy_loss = 0.0
        value_loss = 0.0
        entropy_loss = 0.0

        for i, graph in enumerate(graph_batch):
            # 将图数据移动到指定设备
            graph = graph.to(self.device)

            # 计算每个智能体的策略和价值
            action_logits, values_tensor = self.model(graph)

            # 计算每个智能体的损失
            for agent_id, action_logit in action_logits.items():
                if (agent_id in advantages and i < len(advantages[agent_id]) and
                        agent_id in returns and i < len(returns[agent_id]) and
                        agent_id in action_probs[i] and agent_id in actions[i]):
                    advantage = advantages[agent_id][i]
                    td_target = returns[agent_id][i]
                    action = actions[i][agent_id]
                    action_prob = action_probs[i][agent_id]

                    # 根据动作类型计算策略损失
                    if isinstance(action, np.ndarray):
                        # 连续动作: 使用高斯分布
                        mean = action_logit
                        std = torch.ones_like(mean) * 0.1
                        normal_dist = torch.distributions.Normal(mean, std)

                        # 计算对数概率
                        action_tensor = torch.tensor(action, dtype=torch.float32, device=self.device)
                        log_prob = normal_dist.log_prob(action_tensor).sum()

                        # 计算熵
                        entropy = normal_dist.entropy().sum()

                        # 计算策略损失
                        policy_loss_agent = -log_prob * advantage
                    else:
                        # 离散动作: 使用Softmax
                        action_probs_tensor = F.softmax(action_logit, dim=-1)

                        # 计算对数概率
                        log_prob = torch.log(action_probs_tensor[action] + 1e-10)

                        # 计算熵
                        entropy = -(action_probs_tensor * torch.log(action_probs_tensor + 1e-10)).sum()

                        # 计算策略损失
                        policy_loss_agent = -log_prob * advantage

                    # 计算价值损失
                    value_loss_agent = F.mse_loss(values_tensor[agent_id],
                                                  torch.tensor([td_target], dtype=torch.float32, device=self.device))

                    # 累加损失
                    policy_loss += policy_loss_agent
                    value_loss += value_loss_agent
                    entropy_loss += entropy

        # 计算总损失
        loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy_loss

        # 反向传播和优化
        self.optimizer.zero_grad()
        loss.backward()

        # 梯度裁剪
        if self.clip_grad > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.clip_grad)

        self.optimizer.step()

        # 记录更新次数
        self.updates += 1

        return loss.item()

    def train_step_ppo(self):
        """
        执行一步PPO训练
        """
        if not self.ppo_buffer['states']:
            return 0.0

        # 提取缓冲区数据
        states = self.ppo_buffer['states']
        actions = self.ppo_buffer['actions']
        old_action_probs = self.ppo_buffer['action_probs']
        old_values = self.ppo_buffer['values']
        rewards = self.ppo_buffer['rewards']
        dones = self.ppo_buffer['dones']

        # 将图数据转换为PyTorch格式
        graph_batch = []

        for state in states:
            # 使用图构建器将状态转换为图
            graph = self.graph_constructor.construct_graph(state)
            # 添加到批次
            graph_batch.append(graph)

        # 计算优势值和回报
        advantages = {}
        returns = {}

        # 初始化
        for agent_id in self.env.agent_ids:
            advantages[agent_id] = []
            returns[agent_id] = []

        # 计算每个智能体的广义优势估计(GAE)和回报
        with torch.no_grad():
            for t in range(len(states)):
                # 获取当前和下一个状态的价值
                if t < len(states) - 1:
                    next_value_dict = old_values[t + 1]
                else:
                    # 对于最后一个状态，使用当前模型计算价值
                    next_graph = self.graph_constructor.construct_graph(states[-1])
                    next_graph = next_graph.to(self.device)
                    _, next_values_tensor = self.model(next_graph)
                    next_value_dict = {agent_id: value.item() for agent_id, value in next_values_tensor.items()}

                # 计算每个智能体的优势值
                for agent_id in self.env.agent_ids:
                    if (agent_id in old_values[t] and agent_id in rewards[t] and
                            agent_id in dones[t] and agent_id in next_value_dict):
                        value = old_values[t][agent_id]
                        reward = rewards[t][agent_id]
                        done = dones[t][agent_id]
                        next_value = next_value_dict[agent_id] if not done else 0.0

                        # 计算时序差分(TD)目标
                        td_target = reward + self.gamma * next_value

                        # 计算TD误差
                        td_error = td_target - value

                        # 记录优势值和回报
                        advantages[agent_id].append(td_error)
                        returns[agent_id].append(td_target)

        # 标准化优势值
        for agent_id in self.env.agent_ids:
            if advantages[agent_id]:
                # 显式指定数据类型为float32
                adv_tensor = torch.tensor(advantages[agent_id], dtype=torch.float32, device=self.device)
                adv_mean = adv_tensor.mean()
                adv_std = adv_tensor.std() + 1e-10
                advantages[agent_id] = (adv_tensor - adv_mean) / adv_std

        # 执行多个训练周期
        losses = []
        for _ in range(4):  # 通常执行3-10个周期
            # 计算策略和价值损失
            policy_loss = 0.0
            value_loss = 0.0
            entropy_loss = 0.0

            for t, graph in enumerate(graph_batch):
                # 将图数据移动到指定设备
                graph = graph.to(self.device)

                # 计算每个智能体的策略和价值
                action_logits, values_tensor = self.model(graph)

                # 计算每个智能体的损失
                for agent_id, action_logit in action_logits.items():
                    if (agent_id in advantages and t < len(advantages[agent_id]) and
                            agent_id in returns and t < len(returns[agent_id]) and
                            agent_id in old_action_probs[t] and agent_id in actions[t]):

                        advantage = advantages[agent_id][t]
                        td_target = returns[agent_id][t]
                        old_action_prob = old_action_probs[t][agent_id]
                        action = actions[t][agent_id]

                        # 根据动作类型计算策略损失
                        if isinstance(action, np.ndarray):
                            # 连续动作: 使用高斯分布
                            mean = action_logit
                            std = torch.ones_like(mean, dtype=torch.float32) * 0.1
                            normal_dist = torch.distributions.Normal(mean, std)

                            # 计算对数概率
                            action_tensor = torch.tensor(action, dtype=torch.float32, device=mean.device)
                            log_prob = normal_dist.log_prob(action_tensor).sum()

                            # 计算熵
                            entropy = normal_dist.entropy().sum()

                            # 计算ratio
                            if isinstance(old_action_prob, torch.Tensor):
                                # 确保在同一设备和相同数据类型
                                old_log_prob = torch.log(
                                    old_action_prob.to(log_prob.device, dtype=torch.float32) + 1e-10)
                            else:
                                old_log_prob = np.log(old_action_prob + 1e-10)
                                old_log_prob = torch.tensor(old_log_prob, dtype=torch.float32, device=log_prob.device)

                            ratio = torch.exp(log_prob - old_log_prob)
                        else:
                            # 离散动作: 使用Softmax
                            action_probs_tensor = F.softmax(action_logit, dim=-1)

                            # 计算对数概率
                            log_prob = torch.log(action_probs_tensor[action] + 1e-10)

                            # 计算熵
                            entropy = -(action_probs_tensor * torch.log(action_probs_tensor + 1e-10)).sum()

                            # 计算ratio - 确保在同一设备和相同数据类型
                            if isinstance(old_action_prob, torch.Tensor):
                                old_log_prob = torch.log(
                                    old_action_prob.to(log_prob.device, dtype=torch.float32) + 1e-10)
                            else:
                                old_log_prob = torch.log(
                                    torch.tensor(old_action_prob, dtype=torch.float32, device=log_prob.device) + 1e-10)

                            ratio = torch.exp(log_prob - old_log_prob)

                        # 裁剪优势值乘以比率
                        clipped_ratio = torch.clamp(ratio, 1.0 - self.ppo_epsilon, 1.0 + self.ppo_epsilon)
                        policy_loss_agent = -torch.min(ratio * advantage, clipped_ratio * advantage)

                        # 计算价值损失
                        td_target_tensor = torch.tensor([td_target], dtype=torch.float32, device=self.device)
                        value_loss_agent = F.mse_loss(values_tensor[agent_id].float(), td_target_tensor)

                        # 累加损失
                        policy_loss += policy_loss_agent
                        value_loss += value_loss_agent
                        entropy_loss += entropy

            # 计算总损失
            loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy_loss

            # 反向传播和优化
            self.optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            if self.clip_grad > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.clip_grad)

            self.optimizer.step()

            # 记录损失
            losses.append(loss.item())

        # 清空PPO缓冲区
        for key in self.ppo_buffer:
            self.ppo_buffer[key] = []

        # 记录更新次数
        self.updates += 1

        # 返回平均损失
        return np.mean(losses)

    def train(self, num_episodes=1000, max_steps_per_episode=1000, save_interval=100, log_interval=10,
              step_log_interval=50):
        """
        训练模型
        """
        import sys

        start_time = time.time()
        print(f"\n===== 开始训练 =====")
        print(f"计划执行 {num_episodes} 个episodes，每个最多 {max_steps_per_episode} 步")
        print(f"当前算法: {self.algorithm}, 学习率: {self.lr}, 折扣因子: {self.gamma}")
        print(f"设备: {self.device}")
        print("-" * 50)

        for episode in range(1, num_episodes + 1):
            episode_start_time = time.time()

            # 重置环境
            states = self.env.reset()

            # 记录episode统计
            episode_reward = {agent_id: 0.0 for agent_id in self.env.agent_ids}
            episode_step = 0

            # 记录性能指标
            episode_extract_purity = []
            episode_raffinate_purity = []
            episode_productivity = []
            episode_cost_components = {
                'base_cost': [],
                'productivity_term': [],
                'penalty_extract_term': [],
                'penalty_raffinate_term': [],
                'total_cost': []
            }

            # 稳态性能评估
            episode_performance_history = []  # 格式: [(reward, extract_purity, raffinate_purity, productivity, cost), ...]
            has_reached_steady_state = False
            steady_state_step = 0

            # 对于A2C和PPO，清空轨迹缓冲区
            if self.algorithm in ['a2c', 'ppo']:
                episode_states = []
                episode_actions = []
                episode_action_probs = []
                episode_values = []
                episode_rewards = []
                episode_dones = []

            # 在控制台更新当前episode状态
            sys.stdout.write(f"\r开始 Episode {episode}/{num_episodes}")
            sys.stdout.flush()

            for step in range(max_steps_per_episode):
                # 控制步骤日志频率
                if step % step_log_interval == 0 and step > 0:
                    # 计算平均奖励
                    avg_step_reward = sum([r for r in episode_reward.values()]) / len(episode_reward)
                    sys.stdout.write(f"\rEpisode {episode}/{num_episodes}, Step {step}/{max_steps_per_episode}, "
                                     f"当前平均奖励: {avg_step_reward:.4f}")
                    sys.stdout.flush()

                # 如果使用DQN，设置探索率
                if self.algorithm == 'dqn':
                    # 线性衰减探索率
                    epsilon = max(0.01, 1.0 - 0.99 * self.total_steps / (num_episodes * max_steps_per_episode * 0.5))
                else:
                    epsilon = 0.0

                # 将状态转换为图
                graph_data = self.graph_constructor.construct_graph(states)

                # 选择动作
                actions, action_info = self.select_action(graph_data, epsilon)

                # 执行动作
                next_states, rewards, dones, infos = self.env.step(actions)

                # 记录episode统计
                for agent_id, reward in rewards.items():
                    episode_reward[agent_id] += reward

                # 记录性能指标和稳态评估数据
                step_reward = 0
                step_extract_purity = 0
                step_raffinate_purity = 0
                step_productivity = 0
                step_cost = 0

                # 收集当前步性能指标
                for agent_id, info_dict in infos.items():
                    if 'extract_purity' in info_dict:
                        step_extract_purity = info_dict['extract_purity']
                        episode_extract_purity.append(step_extract_purity)
                    if 'raffinate_purity' in info_dict:
                        step_raffinate_purity = info_dict['raffinate_purity']
                        episode_raffinate_purity.append(step_raffinate_purity)
                    if 'productivity' in info_dict:
                        step_productivity = info_dict['productivity']
                        episode_productivity.append(step_productivity)
                    if 'cost_components' in info_dict and 'total_cost' in info_dict['cost_components']:
                        step_cost = info_dict['cost_components']['total_cost']
                        for comp_name, comp_value in info_dict['cost_components'].items():
                            if comp_name in episode_cost_components:
                                episode_cost_components[comp_name].append(comp_value)

                    step_reward += rewards[agent_id]
                    # 只需记录一次
                    break

                # 添加到性能历史
                step_reward = step_reward / len(rewards)  # 平均奖励
                episode_performance_history.append((
                    step_reward,
                    step_extract_purity,
                    step_raffinate_purity,
                    step_productivity,
                    step_cost
                ))

                # 检测稳态状态 - 基于奖励
                if not has_reached_steady_state and step >= self.steady_state_window:
                    if self.is_steady_state(episode_performance_history, 0):  # 0表示检查奖励
                        has_reached_steady_state = True
                        steady_state_step = step
                        sys.stdout.write(f"\rEpisode {episode}/{num_episodes} - 稳态达成于步骤 {steady_state_step}")
                        sys.stdout.flush()

                # 如果使用DQN，将经验添加到回放缓冲区
                if self.algorithm == 'dqn':
                    self.replay_buffer.add(states, actions, rewards, next_states, dones)

                    # 执行训练步骤
                    loss = self.train_step_dqn()

                    # 更新TensorBoard日志
                    if loss > 0:
                        self.writer.add_scalar('Loss/dqn', loss, self.total_steps)

                # 如果使用A2C或PPO，记录轨迹
                elif self.algorithm in ['a2c', 'ppo']:
                    # 记录状态、动作、动作概率、价值和奖励
                    episode_states.append(states)
                    episode_actions.append(actions)
                    episode_action_probs.append(action_info['action_probs'])
                    episode_values.append(action_info['values'])
                    episode_rewards.append(rewards)
                    episode_dones.append(dones)

                    # 如果使用PPO，将轨迹添加到PPO缓冲区
                    if self.algorithm == 'ppo':
                        self.ppo_buffer['states'].append(states)
                        self.ppo_buffer['actions'].append(actions)
                        self.ppo_buffer['action_probs'].append(action_info['action_probs'])
                        self.ppo_buffer['values'].append(action_info['values'])
                        self.ppo_buffer['rewards'].append(rewards)
                        self.ppo_buffer['dones'].append(dones)

                    # 如果达到一定步数或episode结束，执行A2C训练步骤
                    if self.algorithm == 'a2c' and (step + 1) % 5 == 0:
                        loss = self.train_step_a2c(
                            episode_states, episode_actions, episode_rewards,
                            episode_states[1:] + [next_states], episode_dones,
                            episode_action_probs, episode_values
                        )

                        # 清空轨迹
                        episode_states = []
                        episode_actions = []
                        episode_action_probs = []
                        episode_values = []
                        episode_rewards = []
                        episode_dones = []

                        # 更新TensorBoard日志
                        if loss > 0:
                            self.writer.add_scalar('Loss/a2c', loss, self.total_steps)

                # 更新状态
                states = next_states

                # 更新步数计数器
                episode_step += 1
                self.total_steps += 1

                # 检查是否完成
                if dones['__all__']:
                    break

            # 如果使用PPO，执行训练步骤
            if self.algorithm == 'ppo' and self.ppo_buffer['states']:
                # 提示PPO训练
                sys.stdout.write(f"\r执行PPO策略优化...                                                      ")
                sys.stdout.flush()

                loss = self.train_step_ppo()

                # 更新TensorBoard日志
                if loss > 0:
                    self.writer.add_scalar('Loss/ppo', loss, self.total_steps)

            # 记录episode统计
            avg_episode_reward = sum(episode_reward.values()) / len(episode_reward)
            self.episode_rewards.append(avg_episode_reward)
            self.episode_lengths.append(episode_step)
            self.episodes += 1

            # 记录性能指标
            if episode_extract_purity:
                mean_extract_purity = np.mean(episode_extract_purity)
                self.extract_purities.append(mean_extract_purity)
                self.writer.add_scalar('Performance/extract_purity', mean_extract_purity, episode)

            if episode_raffinate_purity:
                mean_raffinate_purity = np.mean(episode_raffinate_purity)
                self.raffinate_purities.append(mean_raffinate_purity)
                self.writer.add_scalar('Performance/raffinate_purity', mean_raffinate_purity, episode)

            if episode_productivity:
                mean_productivity = np.mean(episode_productivity)
                self.productivities.append(mean_productivity)
                self.writer.add_scalar('Performance/productivity', mean_productivity, episode)

            # 记录成本组成
            for comp_name, comp_values in episode_cost_components.items():
                if comp_values:
                    mean_comp_value = np.mean(comp_values)
                    self.cost_components_history[comp_name].append(mean_comp_value)
                    self.writer.add_scalar(f'Cost/{comp_name}', mean_comp_value, episode)

            # 记录稳态性能
            if has_reached_steady_state:
                # 从稳态开始点计算平均性能
                steady_data = episode_performance_history[steady_state_step:]

                # 计算稳态平均性能
                steady_reward = np.mean([d[0] for d in steady_data])
                steady_extract = np.mean([d[1] for d in steady_data])
                steady_raffinate = np.mean([d[2] for d in steady_data])
                steady_productivity = np.mean([d[3] for d in steady_data])
                steady_cost = np.mean([d[4] for d in steady_data])

                # 记录稳态性能
                self.steady_state_rewards.append(steady_reward)
                self.steady_state_extract_purities.append(steady_extract)
                self.steady_state_raffinate_purities.append(steady_raffinate)
                self.steady_state_productivities.append(steady_productivity)
                self.steady_state_costs.append(steady_cost)

                # 写入TensorBoard
                self.writer.add_scalar('SteadyState/reward', steady_reward, episode)
                self.writer.add_scalar('SteadyState/extract_purity', steady_extract, episode)
                self.writer.add_scalar('SteadyState/raffinate_purity', steady_raffinate, episode)
                self.writer.add_scalar('SteadyState/productivity', steady_productivity, episode)
                self.writer.add_scalar('SteadyState/cost', steady_cost, episode)
            else:
                # 如果没有达到稳态，使用最后一部分数据作为近似
                last_window = min(self.steady_state_window, len(episode_performance_history))
                if last_window > 0:
                    end_data = episode_performance_history[-last_window:]

                    # 计算近似稳态性能
                    approx_steady_reward = np.mean([d[0] for d in end_data])
                    approx_steady_extract = np.mean([d[1] for d in end_data])
                    approx_steady_raffinate = np.mean([d[2] for d in end_data])
                    approx_steady_productivity = np.mean([d[3] for d in end_data])
                    approx_steady_cost = np.mean([d[4] for d in end_data])

                    # 记录近似稳态性能
                    self.steady_state_rewards.append(approx_steady_reward)
                    self.steady_state_extract_purities.append(approx_steady_extract)
                    self.steady_state_raffinate_purities.append(approx_steady_raffinate)
                    self.steady_state_productivities.append(approx_steady_productivity)
                    self.steady_state_costs.append(approx_steady_cost)

                    # 写入TensorBoard (标记为近似)
                    self.writer.add_scalar('SteadyState/reward_approx', approx_steady_reward, episode)
                    self.writer.add_scalar('SteadyState/extract_purity_approx', approx_steady_extract, episode)
                    self.writer.add_scalar('SteadyState/raffinate_purity_approx', approx_steady_raffinate, episode)
                    self.writer.add_scalar('SteadyState/productivity_approx', approx_steady_productivity, episode)
                    self.writer.add_scalar('SteadyState/cost_approx', approx_steady_cost, episode)

            # 计算平均奖励
            avg_reward = np.mean(self.episode_rewards[-min(100, len(self.episode_rewards)):])

            # 更新TensorBoard日志
            self.writer.add_scalar('Reward/episode', self.episode_rewards[-1], episode)
            self.writer.add_scalar('Reward/average', avg_reward, episode)
            self.writer.add_scalar('Episode/length', episode_step, episode)

            # 计算本episode耗时
            episode_time = time.time() - episode_start_time
            total_time = time.time() - start_time

            # 输出训练进度
            # 更复杂的进度打印，包含更多信息
            progress_msg = (f"\rEpisode {episode}/{num_episodes} 完成 | "
                            f"步数: {episode_step} | "
                            f"奖励: {self.episode_rewards[-1]:.4f} | "
                            f"平均奖励(最近100): {avg_reward:.4f}")

            # 添加生产率信息
            if hasattr(self, 'productivities') and self.productivities:
                progress_msg += f" | 生产率: {self.productivities[-1]:.6f}"
            # 添加稳态性能指标到进度消息
            if has_reached_steady_state:
                progress_msg += f" | 稳态(步骤{steady_state_step}+): "
                progress_msg += f"萃取纯度={self.steady_state_extract_purities[-1]:.4f}, "
                progress_msg += f"萃余纯度={self.steady_state_raffinate_purities[-1]:.4f}"
            else:
                if hasattr(self, 'extract_purities') and self.extract_purities:
                    progress_msg += f" | 萃取纯度: {self.extract_purities[-1]:.4f}"
                if hasattr(self, 'raffinate_purities') and self.raffinate_purities:
                    progress_msg += f" | 萃余纯度: {self.raffinate_purities[-1]:.4f}"

            progress_msg += f" | 耗时: {episode_time:.2f}s | 总耗时: {total_time:.2f}s"

            if episode % log_interval == 0:
                # 每log_interval个episode打印一次完整信息
                print(progress_msg)
            else:
                # 其他情况只更新当前行
                sys.stdout.write(progress_msg)
                sys.stdout.flush()

            # 保存模型
            if episode % save_interval == 0:
                save_path = f"{self.log_dir}/model_ep{episode}.pt"
                self.save_model(save_path)
                print(f"\n模型已保存到: {save_path}")

        # 保存最终模型
        self.save_model(f"{self.log_dir}/model_final.pt")
        print("\n===== 训练完成 =====")
        print(f"总训练时间: {time.time() - start_time:.2f}s")
        print(f"总步数: {self.total_steps}")
        print(f"最终平均奖励(最近100): {avg_reward:.4f}")

        # 打印最终性能指标
        if hasattr(self, 'extract_purities') and self.extract_purities:
            final_extract_purity = np.mean(self.extract_purities[-min(100, len(self.extract_purities)):])
            print(f"最终萃取纯度(最近100): {final_extract_purity:.4f}")
        if hasattr(self, 'raffinate_purities') and self.raffinate_purities:
            final_raffinate_purity = np.mean(self.raffinate_purities[-min(100, len(self.raffinate_purities)):])
            print(f"最终萃余纯度(最近100): {final_raffinate_purity:.4f}")
        if hasattr(self, 'productivities') and self.productivities:
            final_productivity = np.mean(self.productivities[-min(100, len(self.productivities)):])
            print(f"最终生产率(最近100): {final_productivity:.4f}")

        # 新增: 收集测试数据并生成分析图表
        print("\n运行简短测试以生成性能分析图...")
        self.test_extract_purities = []
        self.test_productivities = []

        # 运行少量测试获取数据点
        _, _, _ = self.test(num_episodes=3, render=False)

        # 确保测试数据被收集，并生成系统响应图和权衡分析图
        if not self.system_trajectory['steps']:
            print("警告: 系统轨迹数据未收集，尝试重新测试...")
            _, _, _ = self.test(num_episodes=1, render=False)

        # 打印稳态性能统计
        if self.steady_state_rewards:
            final_steady_reward = np.mean(self.steady_state_rewards[-min(10, len(self.steady_state_rewards)):])
            final_steady_extract = np.mean(
                self.steady_state_extract_purities[-min(10, len(self.steady_state_extract_purities)):])
            final_steady_raffinate = np.mean(
                self.steady_state_raffinate_purities[-min(10, len(self.steady_state_raffinate_purities)):])
            final_steady_productivity = np.mean(
                self.steady_state_productivities[-min(10, len(self.steady_state_productivities)):])

            print("\n===== 最终稳态性能 =====")
            print(f"稳态奖励: {final_steady_reward:.4f}")
            print(f"稳态萃取纯度: {final_steady_extract:.4f}")
            print(f"稳态萃余纯度: {final_steady_raffinate:.4f}")
            print(f"稳态生产率: {final_steady_productivity:.6f}")

        # 关闭TensorBoard写入器
        self.writer.close()

    def save_model(self, path):
        """
        保存模型

        参数:
        -----
        path: str
            保存路径
        """
        # 创建目录(如果不存在)
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'total_steps': self.total_steps,
            'episodes': self.episodes,
            'updates': self.updates,
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'extract_purities': getattr(self, 'extract_purities', []),
            'raffinate_purities': getattr(self, 'raffinate_purities', []),
            'productivities': getattr(self, 'productivities', []),
            'cost_components_history': getattr(self, 'cost_components_history', {}),
            # 新增稳态性能指标
            'steady_state_rewards': getattr(self, 'steady_state_rewards', []),
            'steady_state_extract_purities': getattr(self, 'steady_state_extract_purities', []),
            'steady_state_raffinate_purities': getattr(self, 'steady_state_raffinate_purities', []),
            'steady_state_productivities': getattr(self, 'steady_state_productivities', []),
            'steady_state_costs': getattr(self, 'steady_state_costs', [])
        }, path)

    def load_model(self, path):
        """
        加载模型

        参数:
        -----
        path: str
            加载路径
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            print(f"Model file {path} does not exist.")
            return False

        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)

        # 加载模型参数
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # 如果使用DQN，更新目标网络
        if self.algorithm == 'dqn':
            self.target_model.load_state_dict(self.model.state_dict())

        # 加载优化器参数
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # 加载训练统计
        self.total_steps = checkpoint['total_steps']
        self.episodes = checkpoint['episodes']
        self.updates = checkpoint['updates']
        self.episode_rewards = checkpoint['episode_rewards']
        self.episode_lengths = checkpoint['episode_lengths']

        # 加载性能指标
        if 'extract_purities' in checkpoint:
            self.extract_purities = checkpoint['extract_purities']
        if 'raffinate_purities' in checkpoint:
            self.raffinate_purities = checkpoint['raffinate_purities']
        if 'productivities' in checkpoint:
            self.productivities = checkpoint['productivities']
        if 'cost_components_history' in checkpoint:
            self.cost_components_history = checkpoint['cost_components_history']

        # 加载稳态性能指标
        if 'steady_state_rewards' in checkpoint:
            self.steady_state_rewards = checkpoint['steady_state_rewards']
        if 'steady_state_extract_purities' in checkpoint:
            self.steady_state_extract_purities = checkpoint['steady_state_extract_purities']
        if 'steady_state_raffinate_purities' in checkpoint:
            self.steady_state_raffinate_purities = checkpoint['steady_state_raffinate_purities']
        if 'steady_state_productivities' in checkpoint:
            self.steady_state_productivities = checkpoint['steady_state_productivities']
        if 'steady_state_costs' in checkpoint:
            self.steady_state_costs = checkpoint['steady_state_costs']

        return True

    # 修改 test 方法，增加稳态性能评估
    def test(self, num_episodes=10, render=True):
        """测试模型"""
        # 设置为评估模式
        self.model.eval()

        # 记录测试统计
        test_rewards = []
        test_lengths = []
        test_extract_purities = []
        test_raffinate_purities = []
        test_productivities = []
        test_cost_components = {
            'base_cost': [],
            'productivity_term': [],
            'penalty_extract_term': [],
            'penalty_raffinate_term': [],
            'total_cost': []
        }

        # 稳态性能评估
        test_steady_state_rewards = []
        test_steady_state_extract_purities = []
        test_steady_state_raffinate_purities = []
        test_steady_state_productivities = []
        test_steady_state_costs = []
        test_steady_state_steps = []

        # 清空系统轨迹数据
        self.system_trajectory = {
            'steps': [],
            'extract_purity': [],
            'raffinate_purity': [],
            'productivity': [],
            'control_inputs': [],
            'reward': []
        }

        # 只记录第一个episode的完整轨迹
        record_trajectory = True

        for episode in range(1, num_episodes + 1):
            # 重置环境
            states = self.env.reset()

            # 记录episode统计
            episode_reward = {agent_id: 0.0 for agent_id in self.env.agent_ids}
            episode_step = 0

            # 记录性能指标
            episode_extract_purity = []
            episode_raffinate_purity = []
            episode_productivity = []
            episode_cost_components = {
                'base_cost': [],
                'productivity_term': [],
                'penalty_extract_term': [],
                'penalty_raffinate_term': [],
                'total_cost': []
            }

            # 稳态性能评估
            episode_performance_history = []  # 格式: [(reward, extract_purity, raffinate_purity, productivity, cost), ...]
            has_reached_steady_state = False
            steady_state_step = 0

            while True:
                # 将状态转换为图
                graph_data = self.graph_constructor.construct_graph(states)

                # 选择动作
                with torch.no_grad():
                    actions, _ = self.select_action(graph_data)

                # 执行动作
                next_states, rewards, dones, infos = self.env.step(actions)

                # 记录episode统计
                for agent_id, reward in rewards.items():
                    episode_reward[agent_id] += reward

                # 记录性能指标和稳态评估数据
                step_reward = 0
                step_extract_purity = 0
                step_raffinate_purity = 0
                step_productivity = 0
                step_cost = 0

                # 收集当前步性能指标
                for agent_id, info_dict in infos.items():
                    if 'extract_purity' in info_dict:
                        step_extract_purity = info_dict['extract_purity']
                        episode_extract_purity.append(step_extract_purity)
                    if 'raffinate_purity' in info_dict:
                        step_raffinate_purity = info_dict['raffinate_purity']
                        episode_raffinate_purity.append(step_raffinate_purity)
                    if 'productivity' in info_dict:
                        step_productivity = info_dict['productivity']
                        episode_productivity.append(step_productivity)
                    if 'cost_components' in info_dict and 'total_cost' in info_dict['cost_components']:
                        step_cost = info_dict['cost_components']['total_cost']
                        for comp_name, comp_value in info_dict['cost_components'].items():
                            if comp_name in episode_cost_components:
                                episode_cost_components[comp_name].append(comp_value)

                    step_reward += rewards[agent_id]
                    # 只需记录一次
                    break

                # 添加到性能历史
                step_reward = step_reward / len(rewards)  # 平均奖励
                episode_performance_history.append((
                    step_reward,
                    step_extract_purity,
                    step_raffinate_purity,
                    step_productivity,
                    step_cost
                ))

                # 新增: 记录系统轨迹（只对第一个episode）
                if record_trajectory and episode == 1:
                    self.system_trajectory['steps'].append(episode_step)
                    self.system_trajectory['extract_purity'].append(step_extract_purity)
                    self.system_trajectory['raffinate_purity'].append(step_raffinate_purity)
                    self.system_trajectory['productivity'].append(step_productivity)

                    # 记录控制输入 - 获取合并后的动作
                    env_action = self.env._combine_actions(actions)
                    self.system_trajectory['control_inputs'].append(env_action)

                    # 记录奖励
                    self.system_trajectory['reward'].append(step_reward)

                # 检测稳态状态 - 基于奖励
                if not has_reached_steady_state and episode_step >= self.steady_state_window:
                    if self.is_steady_state(episode_performance_history, 0):  # 0表示检查奖励
                        has_reached_steady_state = True
                        steady_state_step = episode_step

                # 渲染环境
                if render:
                    self.env.render()

                # 更新状态
                states = next_states

                # 更新步数计数器
                episode_step += 1

                # 检查是否完成
                if dones['__all__']:
                    break

            # 记录episode统计
            avg_reward = np.mean([reward for reward in episode_reward.values()])
            test_rewards.append(avg_reward)
            test_lengths.append(episode_step)

            # 记录性能指标
            if episode_extract_purity:
                test_extract_purities.append(np.mean(episode_extract_purity))
            if episode_raffinate_purity:
                test_raffinate_purities.append(np.mean(episode_raffinate_purity))
            if episode_productivity:
                test_productivities.append(np.mean(episode_productivity))

            # 记录成本组成
            for comp_name, comp_values in episode_cost_components.items():
                if comp_values and comp_name in test_cost_components:
                    test_cost_components[comp_name].append(np.mean(comp_values))

            # 记录稳态性能
            if has_reached_steady_state:
                # 从稳态开始点计算平均性能
                steady_data = episode_performance_history[steady_state_step:]

                # 计算稳态平均性能
                steady_reward = np.mean([d[0] for d in steady_data])
                steady_extract = np.mean([d[1] for d in steady_data])
                steady_raffinate = np.mean([d[2] for d in steady_data])
                steady_productivity = np.mean([d[3] for d in steady_data])
                steady_cost = np.mean([d[4] for d in steady_data])

                # 记录稳态性能
                test_steady_state_rewards.append(steady_reward)
                test_steady_state_extract_purities.append(steady_extract)
                test_steady_state_raffinate_purities.append(steady_raffinate)
                test_steady_state_productivities.append(steady_productivity)
                test_steady_state_costs.append(steady_cost)
                test_steady_state_steps.append(steady_state_step)
            else:
                # 如果没有达到稳态，使用最后一部分数据作为近似
                last_window = min(self.steady_state_window, len(episode_performance_history))
                if last_window > 0:
                    end_data = episode_performance_history[-last_window:]

                    # 计算近似稳态性能
                    approx_steady_reward = np.mean([d[0] for d in end_data])
                    approx_steady_extract = np.mean([d[1] for d in end_data])
                    approx_steady_raffinate = np.mean([d[2] for d in end_data])
                    approx_steady_productivity = np.mean([d[3] for d in end_data])
                    approx_steady_cost = np.mean([d[4] for d in end_data])

                    # 记录近似稳态性能
                    test_steady_state_rewards.append(approx_steady_reward)
                    test_steady_state_extract_purities.append(approx_steady_extract)
                    test_steady_state_raffinate_purities.append(approx_steady_raffinate)
                    test_steady_state_productivities.append(approx_steady_productivity)
                    test_steady_state_costs.append(approx_steady_cost)
                    test_steady_state_steps.append(episode_step - last_window)  # 近似稳态开始点

            # 输出测试进度
            steady_state_info = ""
            if has_reached_steady_state:
                steady_state_info = f" | 稳态(步骤{steady_state_step}+)"

            progress_msg = f"测试 Episode {episode}/{num_episodes} | 步骤: {episode_step}{steady_state_info} | 奖励: {avg_reward:.4f}"
            if episode_extract_purity:
                progress_msg += f" | 萃取纯度: {np.mean(episode_extract_purity):.4f}"
            if episode_raffinate_purity:
                progress_msg += f" | 萃余纯度: {np.mean(episode_raffinate_purity):.4f}"
            print(progress_msg)

        # 新增: 保存测试性能指标用于后续分析
        self.test_extract_purities = test_extract_purities
        self.test_productivities = test_productivities

        # 计算平均统计
        mean_reward = np.mean(test_rewards)
        mean_length = np.mean(test_lengths)
        mean_extract_purity = np.mean(test_extract_purities) if test_extract_purities else 0
        mean_raffinate_purity = np.mean(test_raffinate_purities) if test_raffinate_purities else 0
        mean_productivity = np.mean(test_productivities) if test_productivities else 0

        # 计算稳态平均统计
        mean_steady_reward = np.mean(test_steady_state_rewards) if test_steady_state_rewards else 0
        mean_steady_extract = np.mean(test_steady_state_extract_purities) if test_steady_state_extract_purities else 0
        mean_steady_raffinate = np.mean(
            test_steady_state_raffinate_purities) if test_steady_state_raffinate_purities else 0
        mean_steady_productivity = np.mean(test_steady_state_productivities) if test_steady_state_productivities else 0
        mean_steady_step = np.mean(test_steady_state_steps) if test_steady_state_steps else 0

        # 计算成本组成平均值
        mean_cost_components = {}
        for comp_name, comp_values in test_cost_components.items():
            if comp_values:
                mean_cost_components[comp_name] = np.mean(comp_values)
            else:
                mean_cost_components[comp_name] = 0

        print(f"\nTest Results | Mean Reward: {mean_reward:.4f} | Mean Length: {mean_length:.2f}")
        print(
            f"Performance | Extract Purity: {mean_extract_purity:.4f} | Raffinate Purity: {mean_raffinate_purity:.4f} | Productivity: {mean_productivity:.6f}")

        if test_steady_state_rewards:
            print(f"\nSteady-State Performance (avg. from step {mean_steady_step:.1f}):")
            print(f"  Reward: {mean_steady_reward:.4f}")
            print(f"  Extract Purity: {mean_steady_extract:.4f}")
            print(f"  Raffinate Purity: {mean_steady_raffinate:.4f}")
            print(f"  Productivity: {mean_steady_productivity:.6f}")

        # 输出成本组成
        cost_components_str = " | ".join([f"{name}: {value:.6f}" for name, value in mean_cost_components.items()])
        print(f"Cost Components | {cost_components_str}")

        # 生成分析图表
        self._plot_system_response()
        self._plot_control_inputs()
        self._plot_tradeoff_analysis()

        # 重新设置为训练模式
        self.model.train()

        # 返回结果，包含性能指标和稳态性能
        return mean_reward, mean_length, {
            'extract_purity': mean_extract_purity,
            'raffinate_purity': mean_raffinate_purity,
            'productivity': mean_productivity,
            'cost_components': mean_cost_components,
            'steady_state': {
                'reward': mean_steady_reward,
                'extract_purity': mean_steady_extract,
                'raffinate_purity': mean_steady_raffinate,
                'productivity': mean_steady_productivity,
                'step': mean_steady_step
            }
        }

    def is_steady_state(self, performance_history, metric_index=0):
        """
        检测系统是否达到稳态

        参数:
        -----
        performance_history: list
            性能历史数据列表
        metric_index: int
            要检查的指标索引

        返回:
        -----
        is_steady: bool
            是否达到稳态
        """
        if len(performance_history) < self.steady_state_window:
            return False

        recent_values = [p[metric_index] for p in performance_history[-self.steady_state_window:]]
        avg_value = np.mean(recent_values)
        std_value = np.std(recent_values)

        # 相对标准差小于阈值视为稳态
        return std_value / (abs(avg_value) + 1e-10) < self.steady_state_threshold

    def _plot_system_response(self):
        """绘制系统响应曲线"""
        if not self.system_trajectory['steps']:
            return

        # 确保保存目录存在
        plots_dir = os.path.join(self.log_dir, 'plots')
        os.makedirs(plots_dir, exist_ok=True)

        plt.figure(figsize=(12, 12))

        # 1. 纯度响应
        plt.subplot(4, 1, 1)
        plt.plot(self.system_trajectory['steps'], self.system_trajectory['extract_purity'], label='Extract Purity',
                 color='blue')
        plt.plot(self.system_trajectory['steps'], self.system_trajectory['raffinate_purity'], label='Raffinate Purity',
                 color='green')
        plt.ylabel('Purity')
        plt.title('System Response Analysis')
        plt.grid(True)
        plt.legend()

        # 2. 生产率响应
        plt.subplot(4, 1, 2)
        plt.plot(self.system_trajectory['steps'], self.system_trajectory['productivity'], label='Productivity',
                 color='red')
        plt.ylabel('Productivity')
        plt.grid(True)
        plt.legend()

        # 3. 奖励响应
        plt.subplot(4, 1, 3)
        plt.plot(self.system_trajectory['steps'], self.system_trajectory['reward'], label='Reward', color='purple')
        plt.ylabel('Reward')
        plt.grid(True)
        plt.legend()

        # 4. 分析稳态区域（使用滑动窗口识别）
        if len(self.system_trajectory['extract_purity']) > self.steady_state_window:
            # 使用滑动窗口识别稳态区域
            window_size = self.steady_state_window
            extract_std = []
            raffinate_std = []

            for i in range(len(self.system_trajectory['extract_purity']) - window_size + 1):
                extract_window = self.system_trajectory['extract_purity'][i:i + window_size]
                raffinate_window = self.system_trajectory['raffinate_purity'][i:i + window_size]

                extract_std.append(np.std(extract_window) / (np.mean(extract_window) + 1e-10))
                raffinate_std.append(np.std(raffinate_window) / (np.mean(raffinate_window) + 1e-10))

            # 绘制相对标准偏差
            plt.subplot(4, 1, 4)
            plt.plot(range(window_size - 1, len(self.system_trajectory['extract_purity'])), extract_std,
                     label='Extract RSD', color='blue')
            plt.plot(range(window_size - 1, len(self.system_trajectory['raffinate_purity'])), raffinate_std,
                     label='Raffinate RSD', color='green')

            # 寻找稳态起始点（当相对标准差低于阈值）
            steady_threshold = self.steady_state_threshold
            for i, (e_std, r_std) in enumerate(zip(extract_std, raffinate_std)):
                if e_std < steady_threshold and r_std < steady_threshold:
                    steady_idx = i + window_size - 1
                    plt.axvline(x=steady_idx, color='red', linestyle='--', label=f'Steady State (step {steady_idx})')

                    # 在其他子图中也标记稳态线
                    for j in range(1, 4):
                        plt.subplot(4, 1, j)
                        plt.axvline(x=steady_idx, color='red', linestyle='--')

                    break

            plt.xlabel('Steps')
            plt.ylabel('Relative Std Dev')
            plt.grid(True)
            plt.legend()
        else:
            # 如果数据点不足，则不进行稳态分析
            plt.subplot(4, 1, 4)
            plt.text(0.5, 0.5, 'Insufficient data for steady-state analysis',
                     horizontalalignment='center', verticalalignment='center', transform=plt.gca().transAxes)
            plt.xlabel('Steps')

        plt.tight_layout()
        plt.savefig(os.path.join(self.log_dir, 'plots', 'system_response.png'))
        plt.close()

    def _plot_control_inputs(self):
        """绘制控制输入变化曲线"""
        if not self.system_trajectory['control_inputs']:
            return

        # 确保保存目录存在
        plots_dir = os.path.join(self.log_dir, 'plots')
        os.makedirs(plots_dir, exist_ok=True)

        # 转换为numpy数组以便处理
        control_inputs = np.array(self.system_trajectory['control_inputs'])
        steps = np.array(self.system_trajectory['steps'])

        plt.figure(figsize=(10, 8))

        # 1. 控制输入随时间变化
        plt.subplot(2, 1, 1)

        # 每个区段流速
        for i in range(control_inputs.shape[1]):
            plt.plot(steps, control_inputs[:, i], label=f'Section {i + 1}')

        plt.ylabel('Flow Rate')
        plt.title('Control Input Analysis')
        plt.grid(True)
        plt.legend()

        # 2. 控制输入变化率
        plt.subplot(2, 1, 2)

        if len(control_inputs) > 1:
            # 计算控制输入的变化率
            control_changes = np.abs(control_inputs[1:] - control_inputs[:-1])

            # 计算总变化和滑动平均
            total_change = np.sum(control_changes, axis=1)
            window = min(10, len(total_change))
            if window > 0:
                moving_avg = np.convolve(total_change, np.ones(window) / window, mode='valid')

                # 绘制变化率和滑动平均
                plt.plot(steps[1:], total_change, label='Control Change Rate', alpha=0.5)
                plt.plot(steps[window:], moving_avg, label=f'Moving Avg ({window} steps)', linewidth=2)

                plt.xlabel('Steps')
                plt.ylabel('Control Change Magnitude')
                plt.grid(True)
                plt.legend()
            else:
                plt.text(0.5, 0.5, 'Insufficient data for change rate analysis',
                         horizontalalignment='center', verticalalignment='center', transform=plt.gca().transAxes)
                plt.xlabel('Steps')
        else:
            plt.text(0.5, 0.5, 'Insufficient data for change rate analysis',
                     horizontalalignment='center', verticalalignment='center', transform=plt.gca().transAxes)
            plt.xlabel('Steps')

        plt.tight_layout()
        plt.savefig(os.path.join(self.log_dir, 'plots', 'control_inputs_analysis.png'))
        plt.close()

    def _plot_tradeoff_analysis(self):
        """绘制纯度-生产率权衡分析"""
        if not hasattr(self, 'extract_purities') or not hasattr(self, 'productivities'):
            return

        if len(self.extract_purities) == 0 or len(self.productivities) == 0:
            return

        # 确保保存目录存在
        plots_dir = os.path.join(self.log_dir, 'plots')
        os.makedirs(plots_dir, exist_ok=True)

        plt.figure(figsize=(10, 8))

        # 1. 纯度-生产率散点图
        plt.subplot(2, 1, 1)

        # 绘制散点图，使用训练期间收集的数据
        plt.scatter(self.extract_purities, self.productivities, alpha=0.6, label='Training Episodes')

        # 如果有测试数据，也添加进来
        if hasattr(self, 'test_extract_purities') and hasattr(self, 'test_productivities'):
            if len(self.test_extract_purities) > 0 and len(self.test_productivities) > 0:
                plt.scatter(self.test_extract_purities, self.test_productivities,
                            color='red', marker='*', s=100, label='Test Episodes')

        # 添加参考线
        plt.axhline(y=0.005, color='green', linestyle='--', label='Target Productivity')
        plt.axvline(x=0.95, color='blue', linestyle='--', label='Target Purity')

        # 绘制理想区域（右上角，高纯度高生产率）
        plt.axvspan(0.95, 1.0, 0.005, plt.ylim()[1], alpha=0.1, color='green', label='Ideal Region')

        plt.xlabel('Extract Purity')
        plt.ylabel('Productivity')
        plt.title('Purity-Productivity Trade-off Analysis')
        plt.grid(True)
        plt.legend()

        # 2. 优化历程中的纯度-生产率路径
        plt.subplot(2, 1, 2)

        # 选择数据点数量
        num_points = min(len(self.extract_purities), len(self.productivities))

        # 不使用cm.viridis，改用简单的颜色渐变
        # 从蓝色渐变到红色
        for i in range(num_points - 1):
            # 计算当前点的颜色，从蓝色到红色的渐变
            progress = i / (num_points - 1) if num_points > 1 else 0.5
            color = (progress, 0, 1 - progress)  # RGB从(0,0,1)蓝色变为(1,0,0)红色

            # 绘制当前段的路径
            plt.plot([self.extract_purities[i], self.extract_purities[i + 1]],
                     [self.productivities[i], self.productivities[i + 1]],
                     color=color, alpha=0.7)

        # 绘制散点，使用简单的蓝到红渐变色
        for i in range(num_points):
            progress = i / (num_points - 1) if num_points > 1 else 0.5
            color = (progress, 0, 1 - progress)  # RGB从(0,0,1)蓝色变为(1,0,0)红色
            plt.scatter(self.extract_purities[i], self.productivities[i],
                        color=color, s=30)

        # 手动创建颜色条的替代方案 - 简单的文本提示
        plt.text(0.02, 0.02, 'Blue: Early episodes',
                 transform=plt.gca().transAxes, color='blue')
        plt.text(0.02, 0.06, 'Red: Later episodes',
                 transform=plt.gca().transAxes, color='red')

        plt.xlabel('Extract Purity')
        plt.ylabel('Productivity')
        plt.title('Optimization Trajectory')
        plt.grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(self.log_dir, 'plots', 'tradeoff_analysis.png'))
        plt.close()


class ProgressiveSMBTrainer(SMBGraphMARLTrainer):
    """渐进式训练器：从短episode开始，逐步增加长度和复杂度"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 训练阶段设置
        self.current_stage = 0
        self.episode_length_schedule = [200, 500, 1000, 2000]

        # 工况变化设置
        self.enable_workload_changes = False
        self.workload_changes = []

        # 阶段评估结果
        self.stage_performance = []

    def train_progressive(self, total_episodes=1000, save_interval=100):
        """渐进式训练方法"""
        episodes_per_stage = total_episodes // len(self.episode_length_schedule)

        for stage, max_steps in enumerate(self.episode_length_schedule):
            self.current_stage = stage

            print(f"\n===== 开始训练阶段 {stage + 1}/{len(self.episode_length_schedule)} =====")
            print(f"Episode长度: {max_steps}步")

            # 根据阶段设置工况变化
            self.configure_workload_changes(stage, max_steps)

            # 使用父类的训练方法进行当前阶段训练
            super().train(
                num_episodes=episodes_per_stage,
                max_steps_per_episode=max_steps,
                save_interval=save_interval,
                log_interval=10
            )

            # 评估当前阶段性能
            performance = self.evaluate_stage()
            self.stage_performance.append(performance)

            # 保存阶段模型
            self.save_model(f"{self.log_dir}/model_stage{stage + 1}.pt")

        print("\n===== 渐进式训练完成 =====")
        self.summarize_training()

    def configure_workload_changes(self, stage, max_steps):
        """配置当前阶段的工况变化"""
        if stage == 0:
            # 阶段1: 无工况变化，学习基本控制
            self.enable_workload_changes = False
            self.workload_changes = []
        elif stage == 1:
            # 阶段2: 单次工况变化
            self.enable_workload_changes = True
            self.workload_changes = [max_steps // 2]  # 在中间位置变化
        else:
            # 后续阶段: 多次工况变化
            self.enable_workload_changes = True
            change_points = []
            interval = max_steps // (stage + 1)
            for i in range(1, stage + 1):
                change_points.append(i * interval)
            self.workload_changes = change_points

    def evaluate_stage(self):
        """评估当前阶段的性能"""
        print(f"\n----- 阶段 {self.current_stage + 1} 性能评估 -----")
        reward, length, metrics = self.test(num_episodes=5, render=False)

        print(f"平均奖励: {reward:.4f}")
        print(f"萃取纯度: {metrics['extract_purity']:.4f}")
        print(f"萃余纯度: {metrics['raffinate_purity']:.4f}")

        if 'steady_state' in metrics:
            ss = metrics['steady_state']
            print(f"稳态达成时间: {ss['step']:.1f}步")
            print(f"稳态性能: 萃取{ss['extract_purity']:.4f}, 萃余{ss['raffinate_purity']:.4f}")

        return {
            'stage': self.current_stage,
            'reward': reward,
            'metrics': metrics
        }

    def summarize_training(self):
        """总结所有阶段的训练结果"""
        print("\n===== 渐进式训练性能总结 =====")
        for stage_data in self.stage_performance:
            stage = stage_data['stage']
            reward = stage_data['reward']
            metrics = stage_data['metrics']

            print(f"阶段 {stage + 1} (长度: {self.episode_length_schedule[stage]}步):")
            print(f"  奖励: {reward:.4f}")
            print(f"  萃取纯度: {metrics['extract_purity']:.4f}")
            print(f"  萃余纯度: {metrics['raffinate_purity']:.4f}")
            if 'steady_state' in metrics:
                ss = metrics['steady_state']
                print(f"  稳态达成时间: {ss['step']:.1f}步")