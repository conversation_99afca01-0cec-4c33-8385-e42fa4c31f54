import numpy as np
from scipy.integrate import solve_ivp
import gym
from gym import spaces
from numba import jit


class SMBModel(gym.Env):
    """
    Simulated Moving Bed (SMB) process model - 简化版

    状态空间：
    - n_cols × n_nodes × 4 = 8列 × 25节点 × 4浓度（2组分的液相和固相浓度）

    控制变量：
    - 4个流速（对应4个区段的独立控制）

    观测空间：
    - 萃取口和萃余口的组分浓度
    - 当前的端口配置模式
    """

    def __init__(self, dt=30, max_steps=1920, n_cols=8, n_nodes=25):
        super(SMBModel, self).__init__()

        # 基本参数
        self.dt = dt  # 采样时间 [s]
        self.max_steps = max_steps  # 最大步数
        self.step_count = 0

        # SMB参数
        self.n_cols = n_cols  # 色谱柱数量
        self.n_nodes = n_nodes  # 每列的离散化节点数
        self.port_switch_interval = 4  # 端口切换间隔(步数)
        self.current_mode = 0  # 当前端口配置

        # 柱参数
        self.L = 1.0  # 柱长 [m]
        self.diameter = 0.1  # 柱直径 [m]
        self.epsilon = 0.66  # 空隙率
        self.kh = np.array([2.0, 2.0])  # 传质阻力系数
        self.D = 1e-5  # 表观扩散系数 [m²/s]

        # 计算参数
        self.area = np.pi * self.diameter ** 2 / 4  # 截面积 [m²]
        self.volume = self.area * self.L  # 体积 [m³]
        self.grid_length = self.L / (self.n_nodes - 1)  # 网格间距 [m]

        # 吸附参数 (Langmuir等温线)
        self.equili_const = np.array([0.5, 0.2])  # 平衡常数
        self.langmuir_coeff = np.array([5.0, 5.0])  # Langmuir饱和容量

        # 亨利常数
        self.H = {
            'A': self.equili_const[0] * self.langmuir_coeff[0],  # A组分亨利常数 (2.5)
            'B': self.equili_const[1] * self.langmuir_coeff[1]  # B组分亨利常数 (1.0)
        }

        # Langmuir常数
        self.K = {
            'A': 0.1,  # A组分平衡常数
            'B': 0.05  # B组分平衡常数
        }

        # 固定流速
        self.section1_flow_rate = 0.022  # 区段1流速 [m/s]
        self.section4_flow_rate = 0.010  # 区段4流速 [m/s]

        # 流速边界
        self.u_min = np.array([0.0100, 0.0100, 0.0135, 0.0135])  # 最小流速 [m/s]
        self.u_max = np.array([0.0135, 0.0135, 0.0165, 0.0165])  # 最大流速 [m/s]
        self.u_default = np.array([0.0130, 0.0130, 0.0140, 0.0140])  # 默认流速 [m/s]

        # 定义动作空间和观测空间 (Gym兼容)
        self.action_space = spaces.Box(
            low=np.array([-1.0, -1.0, -1.0, -1.0]),
            high=np.array([1.0, 1.0, 1.0, 1.0]),
            dtype=np.float32
        )

        self.observation_space = spaces.Dict({
            "extract_conc": spaces.Box(
                low=np.array([0.0, 0.0]),  # [A, B]
                high=np.array([1.0, 1.0]),
                dtype=np.float32
            ),
            "raffinate_conc": spaces.Box(
                low=np.array([0.0, 0.0]),  # [A, B]
                high=np.array([1.0, 1.0]),
                dtype=np.float32
            ),
            "mode": spaces.Discrete(8),  # 当前端口配置
            "control_input": spaces.Box(
                low=self.u_min,
                high=self.u_max,
                dtype=np.float32
            )
        })

        # 初始化系统状态
        self.state = np.zeros((self.n_cols, self.n_nodes, 4))

        # 进料浓度
        self.feed_conc = {'A': 0.5, 'B': 0.5}  # 50/50消旋混合物

        # 参考纯度
        self.purity_reference = 0.99

        # 预计算常数
        self.dx = self.L / (self.n_nodes - 1)  # 网格间距
        self.dx2 = self.dx * self.dx  # 网格间距平方
        self.ee = (1 - self.epsilon) / self.epsilon  # 预计算因子

        # 质量平衡跟踪
        self.total_feed_in_A = 0.0
        self.total_feed_in_B = 0.0
        self.total_extract_out_A = 0.0
        self.total_extract_out_B = 0.0
        self.total_raffinate_out_A = 0.0
        self.total_raffinate_out_B = 0.0

        # 萃取罐和萃余罐
        self.extract_tank = np.array([0.0001, 0.0])  # A和B组分浓度
        self.raffinate_tank = np.array([0.0, 0.01])  # A和B组分浓度

        # 为Numba函数转换参数
        self.H_A = self.H['A']
        self.H_B = self.H['B']
        self.K_A = self.K['A']
        self.K_B = self.K['B']

        # 初始化状态
        self.reset()

        # 添加工况变化相关属性
        self.enable_workload_changes = False
        self.workload_changes = []
        self.current_workload = 'normal'
        self.available_workloads = ['normal', 'high_feed', 'low_feed', 'disturbed']

        # 添加噪声和扰动控制参数
        self.add_execution_noise = True  # 是否添加执行噪声
        self.add_parameter_drift = True  # 是否添加参数漂移
        self.noise_level = 0.01  # 执行噪声水平 (1%)
        self.drift_probability = 0.1  # 参数漂移概率

        # 参数记录，用于跟踪漂移
        self.initial_kh = self.kh.copy()
        self.initial_D = self.D

    # Numba优化的Langmuir平衡关系计算
    @staticmethod
    @jit(nopython=True)
    def _langmuir_eq_numba(c, H_i, K_A, K_B):
        """Langmuir平衡关系计算 - Numba优化"""
        q_eq = H_i * c / (1 + K_A * c + K_B * c)
        return q_eq

    @staticmethod
    @jit(nopython=True)
    def _compute_pde_dynamics(state, velocities, dx, dx2, ee, kh, H_A, H_B, K_A, K_B, D, inlet_concs):
        """Numba优化的PDE动力学计算"""
        n_cols, n_nodes, _ = state.shape
        dydt = np.zeros_like(state)

        for col in range(n_cols):
            # 获取液相浓度
            c_A = state[col, :, 0]  # A组分液相
            c_B = state[col, :, 1]  # B组分液相
            q_A = state[col, :, 2]  # A组分固相
            q_B = state[col, :, 3]  # B组分固相

            # 当前柱的流速
            v = velocities[col]

            # 设置入口条件
            c_inlet_A = inlet_concs[col, 0]
            c_inlet_B = inlet_concs[col, 1]

            # 直接设置入口节点
            state[col, 0, 0] = c_inlet_A
            state[col, 0, 1] = c_inlet_B

            # 入口节点导数设为0
            dydt[col, 0, 0] = 0.0
            dydt[col, 0, 1] = 0.0

            # 计算固相入口导数
            q_A_eq_inlet = H_A * c_inlet_A / (1 + K_A * c_inlet_A + K_B * c_inlet_B)
            q_B_eq_inlet = H_B * c_inlet_B / (1 + K_A * c_inlet_A + K_B * c_inlet_B)

            dydt[col, 0, 2] = kh[0] * (q_A_eq_inlet - q_A[0])
            dydt[col, 0, 3] = kh[1] * (q_B_eq_inlet - q_B[0])

            # 计算内部节点
            for i in range(1, n_nodes - 1):
                # 二阶导数近似(扩散项)
                d2c_A_dx2 = (c_A[i + 1] - 2 * c_A[i] + c_A[i - 1]) / dx2
                d2c_B_dx2 = (c_B[i + 1] - 2 * c_B[i] + c_B[i - 1]) / dx2

                # 一阶导数近似(对流项)
                # 使用逆风格式基于流向
                if v > 0:
                    dc_A_dx = (c_A[i] - c_A[i - 1]) / dx
                    dc_B_dx = (c_B[i] - c_B[i - 1]) / dx
                else:
                    dc_A_dx = (c_A[i + 1] - c_A[i]) / dx
                    dc_B_dx = (c_B[i + 1] - c_B[i]) / dx

                # 传质项 - 使用Langmuir等温线
                q_A_eq = H_A * c_A[i] / (1 + K_A * c_A[i] + K_B * c_B[i])
                q_B_eq = H_B * c_B[i] / (1 + K_A * c_A[i] + K_B * c_B[i])

                # 计算固相浓度变化率
                dq_A_dt = kh[0] * (q_A_eq - q_A[i])
                dq_B_dt = kh[1] * (q_B_eq - q_B[i])

                # 计算液相浓度变化率
                dc_A_dt = D * d2c_A_dx2 - v * dc_A_dx - ee * dq_A_dt
                dc_B_dt = D * d2c_B_dx2 - v * dc_B_dx - ee * dq_B_dt

                # 存储导数
                dydt[col, i, 0] = dc_A_dt
                dydt[col, i, 1] = dc_B_dt
                dydt[col, i, 2] = dq_A_dt
                dydt[col, i, 3] = dq_B_dt

            # 出口边界条件 (零梯度)
            dydt[col, -1, 0] = dydt[col, -2, 0]
            dydt[col, -1, 1] = dydt[col, -2, 1]
            dydt[col, -1, 2] = dydt[col, -2, 2]
            dydt[col, -1, 3] = dydt[col, -2, 3]

        return dydt

    def pde_dynamics(self, t, y, section_flow_rates):
        """SMB系统的PDE模型 - 使用Numba优化计算"""
        # 重塑状态从1D数组到结构化数组
        state = y.reshape((self.n_cols, self.n_nodes, 4))

        # 根据当前端口配置计算每列流速
        velocities = self.compute_velocities(section_flow_rates)

        # 预计算每列的入口浓度
        inlet_concs = np.zeros((self.n_cols, 2))
        for col in range(self.n_cols):
            inlet_concs[col, 0], inlet_concs[col, 1] = self.get_inlet_conc(col)

        # 使用Numba优化函数计算PDE动力学
        dydt = self._compute_pde_dynamics(
            state, velocities, self.dx, self.dx2, self.ee,
            self.kh, self.H_A, self.H_B, self.K_A, self.K_B,
            self.D, inlet_concs
        )

        return dydt.flatten()

    def compute_velocities(self, section_flow_rates):
        """计算每列的流速"""
        # 将控制输入转换为8列的完整流速
        u = self._total_inputs(section_flow_rates)

        # 根据当前端口配置将列映射到区段
        role_index = np.array([0, 1, 2, 3, 4, 5, 6, 7], dtype=int)
        role_index = np.concatenate((role_index[8 - self.current_mode:8], role_index[0:8 - self.current_mode]))

        # 根据区段映射为每列分配流速
        velocities = np.array([u[role_index[i]] for i in range(self.n_cols)])

        return velocities

    def _total_inputs(self, u):
        """将4个控制输入转换为8个柱流速"""
        total_u = np.array([
            self.section1_flow_rate, self.section1_flow_rate,  # 区段1固定流速
            0.0, 0.0, 0.0, 0.0,  # 中间4个区段由控制器控制
            self.section4_flow_rate, self.section4_flow_rate  # 区段4固定流速
        ])

        # 将4个控制变量分配到中间4个位置
        total_u[2:6] = u

        return total_u

    def get_inlet_conc(self, col):
        """获取一列的入口浓度"""
        # 确定列在周期中的位置
        role_index = (col - self.current_mode) % self.n_cols

        # 默认(来自前一列)
        prev_col = (col - 1) % self.n_cols
        c_inlet_A = self.state[prev_col, -1, 0]
        c_inlet_B = self.state[prev_col, -1, 1]

        # 进料入口(区段3和4之间)
        if role_index == 4:
            c_inlet_A = self.feed_conc['A']
            c_inlet_B = self.feed_conc['B']

        # 脱附剂入口(区段1和2之间)
        elif role_index == 0:
            c_inlet_A = 0.0
            c_inlet_B = 0.0

        return c_inlet_A, c_inlet_B

    def calculate_purities(self):
        """计算萃取口和萃余口的纯度"""
        # 萃取口(区段1和2之间)
        extract_col = np.where([(col - self.current_mode) % self.n_cols == 1 for col in range(self.n_cols)])[0][0]
        extract_A = self.state[extract_col, -1, 0]
        extract_B = self.state[extract_col, -1, 1]

        # 萃余口(区段3和4之间)
        raffinate_col = np.where([(col - self.current_mode) % self.n_cols == 5 for col in range(self.n_cols)])[0][0]
        raffinate_A = self.state[raffinate_col, -1, 0]
        raffinate_B = self.state[raffinate_col, -1, 1]

        # 计算纯度
        pe = extract_A / (extract_A + extract_B + 1e-10)
        pr = raffinate_B / (raffinate_A + raffinate_B + 1e-10)

        return pe, pr

    def _normalize_action(self, action):
        """归一化动作"""
        # 从[-1, 1]缩放到[0, 1]
        action_scaled = (action + 1) / 2.0

        # 从[0, 1]缩放到实际控制范围
        u = self.u_min + action_scaled * (self.u_max - self.u_min)

        return u

    def update_feed_concentration(self):
        """更新进料浓度(扰动)"""
        # 实现自相关噪声
        noise = np.random.normal(0, 0.015)

        # 添加周期性扰动项 - 模拟上游工序波动
        if self.add_parameter_drift:
            cycle_noise = 0.03 * np.sin(2 * np.pi * self.step_count / 500)  # 周期性波动
            noise += cycle_noise

        self.feed_conc['A'] = 0.5 + 0.1 * (self.feed_conc['A'] - 0.5) * self.dt / 3600 + noise

        # 确保总浓度保持为1.0
        self.feed_conc['B'] = 1.0 - self.feed_conc['A']

        # 确保浓度在合理范围内
        self.feed_conc['A'] = max(0.3, min(0.7, self.feed_conc['A']))
        self.feed_conc['B'] = max(0.3, min(0.7, self.feed_conc['B']))

    def reset(self):
        """重置环境"""
        # 重置步数计数器
        self.step_count = 0

        # 重置端口配置
        self.current_mode = 0

        # 重置进料浓度
        self.feed_conc = {'A': 0.5, 'B': 0.5}

        # 初始化所有浓度为零
        self.state = np.zeros((self.n_cols, self.n_nodes, 4))

        # 在进料位置添加少量初始浓度
        feed_col = np.where([(col - self.current_mode) % self.n_cols == 4 for col in range(self.n_cols)])[0][0]
        feed_node = 0
        self.state[feed_col, feed_node, 0] = self.feed_conc['A']
        self.state[feed_col, feed_node, 1] = self.feed_conc['B']

        # 初始化控制输入为默认值
        self.u = self.u_default.copy()

        # 重置质量平衡跟踪
        self.total_feed_in_A = 0.0
        self.total_feed_in_B = 0.0
        self.total_extract_out_A = 0.0
        self.total_extract_out_B = 0.0
        self.total_raffinate_out_A = 0.0
        self.total_raffinate_out_B = 0.0

        # 重置萃取罐和萃余罐
        self.extract_tank = np.array([0.0001, 0.0])
        self.raffinate_tank = np.array([0.0, 0.01])

        # 获取当前系统观测值
        return self._get_observation()

    def _calculate_reward(self, pe, pr):
        """计算奖励 - 优化版"""
        # 设置目标参数
        purity_target = 0.95
        productivity_target = 0.005

        # 生产率计算
        productivity = self.u[2] - self.u[1]  # 区段3 - 区段2

        # 1. 纯度奖励/惩罚 - 更强调目标纯度的梯度
        # 使用更陡的sigmoid曲线，在目标点附近提供更强梯度
        extract_purity_reward = 2.5 * (1.0 / (1.0 + np.exp(-12 * (pe - purity_target))) - 0.5)
        raffinate_purity_reward = 2.5 * (1.0 / (1.0 + np.exp(-12 * (pr - purity_target))) - 0.5)

        # 2. 生产率奖励 - 修改超过目标后的增长曲线
        productivity_normalized = productivity / productivity_target
        if productivity_normalized <= 1.0:
            # 目标以下保持线性
            productivity_reward = 1.0 * productivity_normalized
        else:
            # 目标以上使用更快饱和的函数
            productivity_reward = 1.0 + 0.3 * (1 - np.exp(-(productivity_normalized - 1.0) * 2.0))
            # 这会使奖励在超过目标后快速趋于饱和，减少盲目提高生产率的动机

        # 3. 溶剂使用惩罚 - 更强调高流速的惩罚
        total_flow = np.sum(self.u)
        solvent_penalty = 0.5 * (total_flow / 0.05)  # 基础惩罚
        # 添加超额惩罚项
        excess_flow = max(0, total_flow - 0.055)  # 0.055作为阈值
        solvent_penalty += 0.3 * (excess_flow / 0.01) ** 2  # 超过阈值后惩罚增长更快

        # 4. 稳定性奖励 - 更鼓励平稳操作
        stability_bonus = 0.0
        if hasattr(self, 'prev_u') and self.prev_u is not None:
            u_change = np.sum(np.abs(self.u - self.prev_u))
            stability_bonus = 0.3 * np.exp(-20.0 * u_change)  # 指数衰减奖励

            # 添加长期稳定性奖励
            if hasattr(self, 'u_history'):
                if len(self.u_history) > 10:
                    # 计算最近10步的控制变化方差
                    recent_changes = np.diff(self.u_history[-10:], axis=0)
                    change_variance = np.mean(np.var(recent_changes, axis=0))
                    stability_bonus += 0.2 * np.exp(-50.0 * change_variance)

            # 更新控制历史
            if not hasattr(self, 'u_history'):
                self.u_history = []
            self.u_history.append(self.u.copy())
            if len(self.u_history) > 20:  # 保持历史长度不超过20
                self.u_history.pop(0)

        self.prev_u = self.u.copy()

        # 5. 新增: 纯度波动惩罚
        purity_stability_penalty = 0.0
        if hasattr(self, 'purity_history'):
            if len(self.purity_history) > 5:
                # 计算最近5步纯度的变化率
                pe_changes = np.diff(self.purity_history[-5:, 0])
                pr_changes = np.diff(self.purity_history[-5:, 1])
                pe_variation = np.mean(np.abs(pe_changes))
                pr_variation = np.mean(np.abs(pr_changes))
                # 惩罚纯度波动
                purity_stability_penalty = 1.0 * (pe_variation + pr_variation)

        # 更新纯度历史
        if not hasattr(self, 'purity_history'):
            self.purity_history = np.zeros((0, 2))
        self.purity_history = np.vstack([self.purity_history, [pe, pr]])
        if len(self.purity_history) > 20:  # 限制历史长度
            self.purity_history = self.purity_history[-20:]

        # 6. 组合权重
        purity_weight = 1.0
        productivity_weight = 1.0
        solvent_weight = 0.4  # 略微增加溶剂惩罚权重
        stability_weight = 0.3  # 增加稳定性奖励权重

        # 计算总奖励
        reward = (purity_weight * (extract_purity_reward + raffinate_purity_reward) +
                  productivity_weight * productivity_reward -
                  solvent_weight * solvent_penalty +
                  stability_weight * stability_bonus -
                  purity_stability_penalty)  # 添加纯度稳定性惩罚

        # 工况适应性奖励
        adaptation_bonus = 0.0
        if self.enable_workload_changes and self.step_count in self.workload_changes:
            adaptation_bonus = 0.5
        elif self.enable_workload_changes and any(
                abs(self.step_count - change) <= 10 for change in self.workload_changes):
            closest_change = min(self.workload_changes, key=lambda x: abs(self.step_count - x))
            steps_since_change = abs(self.step_count - closest_change)
            adaptation_bonus = 0.5 * (1.0 - steps_since_change / 10)

        reward += adaptation_bonus

        # 保存奖励组成用于分析（包括未裁剪值）
        self.cost_components = {
            'extract_purity_reward': purity_weight * extract_purity_reward,
            'raffinate_purity_reward': purity_weight * raffinate_purity_reward,
            'productivity_reward': productivity_weight * productivity_reward,
            'solvent_penalty': -solvent_weight * solvent_penalty,
            'stability_bonus': stability_weight * stability_bonus,
            'adaptation_bonus': adaptation_bonus,
            'unclipped_reward': reward,
            'total_reward': reward
        }

        # 使用软裁剪代替硬裁剪
        clip_scale = 3.0
        clipped_reward = clip_scale * np.tanh(reward / clip_scale)

        # 保存裁剪后的奖励
        self.cost_components['clipped_reward'] = clipped_reward

        return clipped_reward

    def _get_observation(self):
        """获取观测"""
        # 计算纯度
        pe, pr = self.calculate_purities()

        # 萃取和萃余浓度
        extract_col = np.where([(col - self.current_mode) % self.n_cols == 1 for col in range(self.n_cols)])[0][0]
        raffinate_col = np.where([(col - self.current_mode) % self.n_cols == 5 for col in range(self.n_cols)])[0][0]

        extract_conc = np.array([
            self.state[extract_col, -1, 0],  # A浓度
            self.state[extract_col, -1, 1]  # B浓度
        ])

        raffinate_conc = np.array([
            self.state[raffinate_col, -1, 0],  # A浓度
            self.state[raffinate_col, -1, 1]  # B浓度
        ])

        return {
            "extract_conc": extract_conc,
            "raffinate_conc": raffinate_conc,
            "mode": self.current_mode,
            "control_input": self.u.copy(),
            # 添加原始状态数据供算法层使用
            "raw_state": {
                "state": self.state.copy(),
                "feed_conc": self.feed_conc.copy(),
                "extract_col": extract_col,
                "raffinate_col": raffinate_col,
                "extract_purity": pe,
                "raffinate_purity": pr,
                "step_count": self.step_count
            }
        }

    def set_workload_changes(self, enabled, change_steps=None):
        """设置工况变化"""
        self.enable_workload_changes = enabled
        if change_steps is not None:
            self.workload_changes = change_steps

    def apply_workload_change(self):
        """应用工况变化"""
        # 随机选择一个不同于当前的工况
        available = [w for w in self.available_workloads if w != self.current_workload]
        self.current_workload = np.random.choice(available)

        print(f"工况变化: 切换到 {self.current_workload}")

        # 根据新工况调整系统参数
        if self.current_workload == 'high_feed':
            # 高A浓度进料
            self.feed_conc = {'A': 0.7, 'B': 0.3}
            # 反应系统参数不会立即变化
            self.kh = self.initial_kh.copy() * np.random.uniform(0.95, 1.05, 2)
            self.D = self.initial_D * np.random.uniform(0.95, 1.05)

        elif self.current_workload == 'low_feed':
            # 低A浓度进料
            self.feed_conc = {'A': 0.3, 'B': 0.7}
            # 不同组分比例可能影响整体扩散行为
            self.D = self.initial_D * np.random.uniform(0.9, 1.1)

        elif self.current_workload == 'disturbed':
            # 综合扰动工况
            # 传质参数显著变化 - 模拟吸附剂性能异常
            self.kh = self.initial_kh.copy() * np.random.uniform(0.8, 1.2, 2)
            # 扩散系数变化 - 模拟流体性质变化
            self.D = self.initial_D * np.random.uniform(0.8, 1.2)
            # 随机进料组成波动
            ratio = np.random.uniform(0.4, 0.6)
            self.feed_conc = {'A': ratio, 'B': 1 - ratio}

    def step(self, action):
        """执行一步"""
        # 增加步数计数器
        self.step_count += 1

        # 检查是否需要变更工况
        if self.enable_workload_changes and self.step_count in self.workload_changes:
            self.apply_workload_change()

        # 添加系统参数随机扰动 - 新增代码
        if self.step_count % 50 == 0 and np.random.random() < 0.2:  # 每50步有20%概率发生参数扰动
            # 传质系数随机波动 - 模拟吸附剂老化/再生效率变化
            self.kh = self.kh * np.random.uniform(0.95, 1.05, 2)
            # 扩散系数随机波动 - 模拟温度/流体组成变化
            self.D = self.D * np.random.uniform(0.97, 1.03)

        # 将标准化动作转换为实际控制输入
        self.u = self._normalize_action(action)

        # 添加执行噪声 - 新增代码
        # 模拟执行器精度限制（如泵的流量波动）
        actual_u = self.u.copy()
        if hasattr(self, 'add_execution_noise') and self.add_execution_noise:
            noise_level = 0.01  # 1%的执行噪声
            actual_u = self.u * (1 + noise_level * np.random.normal(0, 1, 4))
            # 确保在合法范围内
            actual_u = np.clip(actual_u, self.u_min, self.u_max)
        else:
            actual_u = self.u

        # 计算源端口和汇端口位置
        feed_col = np.where([(col - self.current_mode) % self.n_cols == 4 for col in range(self.n_cols)])[0][0]
        extract_col = np.where([(col - self.current_mode) % self.n_cols == 1 for col in range(self.n_cols)])[0][0]
        raffinate_col = np.where([(col - self.current_mode) % self.n_cols == 5 for col in range(self.n_cols)])[0][0]
        desorbent_col = np.where([(col - self.current_mode) % self.n_cols == 0 for col in range(self.n_cols)])[0][0]

        # 跟踪物质平衡
        # 计算各流率
        total_u = self._total_inputs(actual_u)
        feed_flow = total_u[4] - total_u[3]  # 区段4 - 区段3
        extract_flow = total_u[1]  # 区段2
        raffinate_flow = total_u[5]  # 区段5

        # 进料
        self.total_feed_in_A += feed_flow * self.feed_conc['A'] * self.dt
        self.total_feed_in_B += feed_flow * self.feed_conc['B'] * self.dt

        # 萃取出口
        self.total_extract_out_A += extract_flow * self.state[extract_col, -1, 0] * self.dt
        self.total_extract_out_B += extract_flow * self.state[extract_col, -1, 1] * self.dt

        # 萃余出口
        self.total_raffinate_out_A += raffinate_flow * self.state[raffinate_col, -1, 0] * self.dt
        self.total_raffinate_out_B += raffinate_flow * self.state[raffinate_col, -1, 1] * self.dt

        # 更新萃取罐和萃余罐浓度
        self.extract_tank[0] += self.state[extract_col, -1, 0] * extract_flow * self.dt
        self.extract_tank[1] += self.state[extract_col, -1, 1] * extract_flow * self.dt
        self.raffinate_tank[0] += self.state[raffinate_col, -1, 0] * raffinate_flow * self.dt
        self.raffinate_tank[1] += self.state[raffinate_col, -1, 1] * raffinate_flow * self.dt

        # 检查是否需要切换端口配置
        if self.step_count % self.port_switch_interval == 0:
            self.current_mode = (self.current_mode + 1) % 8

        # 求解系统动力学方程
        t_span = [0, self.dt]
        y0 = self.state.flatten()

        # 优化: 根据扩散系数判断问题刚性
        if self.D < 1e-4:  # 刚性问题
            result = solve_ivp(
                lambda t, y: self.pde_dynamics(t, y, actual_u),
                t_span,
                y0,
                method='BDF',  # 刚性方程使用BDF
                rtol=1e-3,
                atol=1e-5,
                max_step=self.dt / 2,
                t_eval=[self.dt]
            )
        else:  # 非刚性问题
            result = solve_ivp(
                lambda t, y: self.pde_dynamics(t, y, actual_u),
                t_span,
                y0,
                method='RK23',  # 非刚性方程使用RK45
                rtol=1e-3,
                atol=1e-5,
                t_eval=[self.dt]
            )

        # 更新状态
        self.state = result.y[:, 0].reshape((self.n_cols, self.n_nodes, 4))

        # 更新进料浓度(扰动)
        self.update_feed_concentration()

        # 计算纯度
        pe, pr = self.calculate_purities()

        # 计算奖励
        reward = self._calculate_reward(pe, pr)

        # 获取观测
        obs = self._get_observation()

        # 检查是否完成
        done = self.step_count >= self.max_steps

        # 计算质量平衡指标
        total_in_system_A = 0
        total_in_system_B = 0

        # 使用简化的计算方式减少内存使用
        for col in range(self.n_cols):
            fluid_A = np.sum(self.state[col, :, 0])
            fluid_B = np.sum(self.state[col, :, 1])
            solid_A = np.sum(self.state[col, :, 2])
            solid_B = np.sum(self.state[col, :, 3])

            total_in_system_A += fluid_A * self.epsilon + solid_A * (1 - self.epsilon)
            total_in_system_B += fluid_B * self.epsilon + solid_B * (1 - self.epsilon)

        # 计算质量平衡误差
        mass_in_A = self.total_feed_in_A
        mass_out_A = self.total_extract_out_A + self.total_raffinate_out_A
        mass_balance_error_A = (mass_in_A - mass_out_A - total_in_system_A) / (mass_in_A + 1e-10)

        mass_in_B = self.total_feed_in_B
        mass_out_B = self.total_extract_out_B + self.total_raffinate_out_B
        mass_balance_error_B = (mass_in_B - mass_out_B - total_in_system_B) / (mass_in_B + 1e-10)

        # 附加信息
        info = {
            "extract_purity": pe,
            "raffinate_purity": pr,
            "productivity": self.u[2] - self.u[1],  # 区段3 - 区段2
            "feed_concentration": self.feed_conc,
            "mass_balance_error_A": mass_balance_error_A,
            "mass_balance_error_B": mass_balance_error_B,
            "total_in_system_A": total_in_system_A,
            "total_in_system_B": total_in_system_B,
            "extract_tank": self.extract_tank,
            "raffinate_tank": self.raffinate_tank,
            "cost_components": self.cost_components,  # 成本组成部分
            # 添加工况信息
            "current_workload": self.current_workload,
            # 添加系统当前状态
            "current_state": {
                "extract_col": extract_col,
                "raffinate_col": raffinate_col,
                "feed_col": feed_col,
                "desorbent_col": desorbent_col,
                "columns_data": self._get_columns_data(),
                "port_positions": {
                    "extract": extract_col,
                    "raffinate": raffinate_col,
                    "feed": feed_col,
                    "desorbent": desorbent_col
                }
            }
        }

        return obs, reward, done, info

    def _get_columns_data(self):
        """获取列状态数据，供算法层使用"""
        columns_data = []

        for col in range(self.n_cols):
            # 计算平均浓度
            avg_fluid_A = np.mean(self.state[col, :, 0])
            avg_fluid_B = np.mean(self.state[col, :, 1])
            avg_solid_A = np.mean(self.state[col, :, 2])
            avg_solid_B = np.mean(self.state[col, :, 3])

            # 计算最大浓度
            max_fluid_A = np.max(self.state[col, :, 0])
            max_fluid_B = np.max(self.state[col, :, 1])

            # 出口浓度
            exit_fluid_A = self.state[col, -1, 0]
            exit_fluid_B = self.state[col, -1, 1]

            # 列在当前模式下的角色
            role = (col - self.current_mode) % self.n_cols

            columns_data.append({
                "column_id": col,
                "role": role,
                "avg_concentration": {
                    "fluid_A": avg_fluid_A,
                    "fluid_B": avg_fluid_B,
                    "solid_A": avg_solid_A,
                    "solid_B": avg_solid_B
                },
                "max_concentration": {
                    "fluid_A": max_fluid_A,
                    "fluid_B": max_fluid_B
                },
                "exit_concentration": {
                    "fluid_A": exit_fluid_A,
                    "fluid_B": exit_fluid_B
                },
                # 提供更详细数据，便于算法层做更深入分析
                "concentration_profile": {
                    "fluid_A": self.state[col, :, 0].copy(),
                    "fluid_B": self.state[col, :, 1].copy(),
                    "solid_A": self.state[col, :, 2].copy(),
                    "solid_B": self.state[col, :, 3].copy()
                }
            })

        return columns_data