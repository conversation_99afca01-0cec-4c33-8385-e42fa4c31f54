import numpy as np
import gym
from gym import spaces
from typing import Dict, List, Tuple, Any, Optional


class SMBMultiAgentEnv:
    """
    SMB多智能体强化学习环境 - 简化版

    基于SMBModel构建的多智能体环境，支持不同的智能体分配方案
    专注于提供基本的MARL环境功能，将图构建等高级特性分离出来

    参数:
    -----
    smb_model: SMBModel
        底层SMB模型，实现了gym.Env接口
    agent_mode: str
        智能体分配模式，可选 'control_based' (4个智能体) 或 'function_based' (2个智能体)
    shared_reward: bool
        是否使用共享奖励，默认为True
    local_reward_weight: float
        局部奖励的权重，范围[0, 1]，默认为0.3
    """

    def __init__(
            self,
            smb_model,
            agent_mode='control_based',
            shared_reward=True,
            local_reward_weight=0.3
    ):
        self.env = smb_model
        self.agent_mode = agent_mode
        self.shared_reward = shared_reward
        self.local_reward_weight = local_reward_weight

        # 根据代理模式定义智能体和其动作空间
        if agent_mode == 'control_based':
            self.n_agents = 4
            self.agent_ids = [f'agent_{i}' for i in range(self.n_agents)]

            # 每个智能体控制一个流速
            self.action_spaces = {
                agent_id: spaces.Box(low=-1.0, high=1.0, shape=(1,), dtype=np.float32)
                for agent_id in self.agent_ids
            }
        elif agent_mode == 'function_based':
            self.n_agents = 2
            self.agent_ids = ['extract_agent', 'raffinate_agent']

            # 萃取智能体控制区段1和2，萃余智能体控制区段3和4
            self.action_spaces = {
                'extract_agent': spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32),
                'raffinate_agent': spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
            }
        else:
            raise ValueError(f"未知的智能体模式: {agent_mode}")

        # 定义观测空间
        self.base_observation_space = spaces.Dict({
            "extract_conc": spaces.Box(
                low=np.array([0.0, 0.0]),
                high=np.array([1.0, 1.0]),
                dtype=np.float32
            ),
            "raffinate_conc": spaces.Box(
                low=np.array([0.0, 0.0]),
                high=np.array([1.0, 1.0]),
                dtype=np.float32
            ),
            "mode": spaces.Discrete(8),
            "control_input": spaces.Box(
                low=np.array([0.01, 0.01, 0.0135, 0.0135]),
                high=np.array([0.0135, 0.0135, 0.0165, 0.0165]),
                dtype=np.float32
            )
        })

        # 每个智能体的观测空间
        self.observation_spaces = {
            agent_id: self.base_observation_space
            for agent_id in self.agent_ids
        }

    def _get_agent_observation(self, agent_id, state):
        """
        获取指定智能体的观测
        简化版：只返回基本观测和智能体相关的控制区域信息
        """
        # 基础观测
        observation = state.copy()

        # 添加区域优先级信息
        if self.agent_mode == 'control_based':
            agent_idx = int(agent_id.split('_')[1])
            primary_columns = [agent_idx * 2, agent_idx * 2 + 1]

            # 添加区域优先级元数据
            observation['region_priority'] = {
                'primary': primary_columns,
                'secondary': [i for i in range(8) if i not in primary_columns]
            }
        else:  # function_based
            if agent_id == 'extract_agent':
                observation['region_priority'] = {
                    'primary': [0, 1, 2, 3],
                    'secondary': [4, 5, 6, 7]
                }
            else:  # raffinate_agent
                observation['region_priority'] = {
                    'primary': [4, 5, 6, 7],
                    'secondary': [0, 1, 2, 3]
                }

        return observation

    def reset(self):
        """
        重置环境并返回初始观测
        """
        state = self.env.reset()

        # 构建每个智能体的观测
        observations = {}
        for agent_id in self.agent_ids:
            observations[agent_id] = self._get_agent_observation(agent_id, state)

        return observations

    def _combine_actions(self, actions_dict):
        """
        将多个智能体的动作合并为环境动作
        """
        if self.agent_mode == 'control_based':
            # 每个智能体控制一个流速
            combined_action = np.zeros(4)
            for i, agent_id in enumerate(self.agent_ids):
                # 检查动作类型
                if isinstance(actions_dict[agent_id], (int, float)):
                    # 如果是整数或浮点数（离散动作或单值连续动作）
                    combined_action[i] = actions_dict[agent_id]
                else:
                    # 否则是数组（多值连续动作）
                    combined_action[i] = actions_dict[agent_id][0]  # 提取标量值
            return combined_action
        else:  # function_based
            # 萃取智能体控制区段1和2，萃余智能体控制区段3和4
            extract_action = actions_dict['extract_agent']
            raffinate_action = actions_dict['raffinate_agent']
            return np.concatenate([extract_action, raffinate_action])

    def _calculate_agent_rewards(self, global_reward, info):
        """计算每个智能体的奖励 - 优化版"""
        rewards = {}

        # 提取性能指标
        extract_purity = info['extract_purity']
        raffinate_purity = info['raffinate_purity']
        productivity = info.get('productivity', 0.0)
        unclipped_reward = info.get('cost_components', {}).get('unclipped_reward', global_reward)

        # 设置目标参考值
        purity_target = 0.95
        productivity_target = 0.005

        # 动态局部奖励权重
        current_step = info.get('step_count', 0)
        stage = min(1.0, current_step / 20000)
        adjusted_weight = max(0.1, self.local_reward_weight * (1.0 - 0.5 * stage))

        if self.shared_reward:
            # 共享奖励基础版本
            for agent_id in self.agent_ids:
                rewards[agent_id] = global_reward

                # 根据智能体角色添加少量特定职责奖励
                if self.agent_mode == 'control_based':
                    if agent_id in ['agent_0', 'agent_1']:  # 萃取相关
                        # 使用与主奖励函数相同的sigmoid函数
                        role_reward = 0.2 * (1.0 / (1.0 + np.exp(-10 * (extract_purity - purity_target))) - 0.5)
                        rewards[agent_id] += role_reward
                    elif agent_id in ['agent_2', 'agent_3']:  # 萃余相关
                        role_reward = 0.2 * (1.0 / (1.0 + np.exp(-10 * (raffinate_purity - purity_target))) - 0.5)
                        rewards[agent_id] += role_reward
                elif self.agent_mode == 'function_based':
                    if agent_id == 'extract_agent':
                        role_reward = 0.2 * (1.0 / (1.0 + np.exp(-10 * (extract_purity - purity_target))) - 0.5)
                        rewards[agent_id] += role_reward
                    elif agent_id == 'raffinate_agent':
                        role_reward = 0.2 * (1.0 / (1.0 + np.exp(-10 * (raffinate_purity - purity_target))) - 0.5)
                        rewards[agent_id] += role_reward
        else:
            # 非共享奖励 - 混合局部和全局奖励
            extract_local_reward = 1.0 / (1.0 + np.exp(-10 * (extract_purity - purity_target))) - 0.5
            raffinate_local_reward = 1.0 / (1.0 + np.exp(-10 * (raffinate_purity - purity_target))) - 0.5

            # 计算生产率局部奖励
            productivity_normalized = productivity / productivity_target
            if productivity_normalized <= 1.0:
                productivity_local_reward = 0.5 * productivity_normalized
            else:
                productivity_local_reward = 0.5 + 0.25 * np.tanh(productivity_normalized - 1.0)

            if self.agent_mode == 'control_based':
                # 区段1和2智能体负责萃取和脱附剂
                rewards['agent_0'] = adjusted_weight * (extract_local_reward + 0.5 * productivity_local_reward) + (
                        1 - adjusted_weight) * global_reward
                rewards['agent_1'] = adjusted_weight * (extract_local_reward + 0.5 * productivity_local_reward) + (
                        1 - adjusted_weight) * global_reward

                # 区段3和4智能体负责萃余和进料
                rewards['agent_2'] = adjusted_weight * (raffinate_local_reward + 0.5 * productivity_local_reward) + (
                        1 - adjusted_weight) * global_reward
                rewards['agent_3'] = adjusted_weight * (raffinate_local_reward + 0.5 * productivity_local_reward) + (
                        1 - adjusted_weight) * global_reward
            else:  # function_based
                rewards['extract_agent'] = adjusted_weight * (
                            extract_local_reward + 0.5 * productivity_local_reward) + (
                                                   1 - adjusted_weight) * global_reward
                rewards['raffinate_agent'] = adjusted_weight * (
                            raffinate_local_reward + 0.5 * productivity_local_reward) + (
                                                     1 - adjusted_weight) * global_reward

        # 奖励平滑处理
        if not hasattr(self, 'prev_rewards'):
            self.prev_rewards = {agent_id: 0.0 for agent_id in self.agent_ids}

        # 平滑系数随训练进度减小
        smoothing_factor = max(0.05, 0.2 * (1.0 - stage))

        # 应用平滑
        for agent_id in self.agent_ids:
            if agent_id in self.prev_rewards:
                rewards[agent_id] = smoothing_factor * self.prev_rewards[agent_id] + (1 - smoothing_factor) * rewards[
                    agent_id]

        # 保存当前奖励用于下一步平滑
        self.prev_rewards = rewards.copy()

        # 使用与主奖励函数相同的软裁剪
        clip_scale = 3.0
        for agent_id in rewards:
            rewards[agent_id] = clip_scale * np.tanh(rewards[agent_id] / clip_scale)

        return rewards

    def step(self, actions_dict):
        """
        执行一步环境交互
        返回原始观测数据，供后续算法层处理
        """
        # 合并智能体动作
        env_action = self._combine_actions(actions_dict)

        # 执行环境步进
        next_state, global_reward, done, info = self.env.step(env_action)

        # 计算每个智能体的奖励
        rewards = self._calculate_agent_rewards(global_reward, info)

        # 构建每个智能体的观测
        observations = {}
        for agent_id in self.agent_ids:
            observations[agent_id] = self._get_agent_observation(agent_id, next_state)

        # 生成每个智能体的终止标志
        dones = {agent_id: done for agent_id in self.agent_ids}
        dones['__all__'] = done

        # 附加信息
        infos = {agent_id: info.copy() for agent_id in self.agent_ids}

        return observations, rewards, dones, infos

    def render(self, mode='human'):
        """
        渲染环境（如果底层环境支持）
        """
        return self.env.render(mode)

    def close(self):
        """
        关闭环境
        """
        return self.env.close()