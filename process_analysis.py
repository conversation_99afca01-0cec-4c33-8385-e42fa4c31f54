import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
import pandas as pd
from typing import Dict, List, Tuple, Any
import os
from plotting import setup_publication_style, get_publication_colors


class ProcessControlAnalyzer:
    """过程控制分析器：提供针对过程控制系统的专业分析工具"""

    def __init__(self, system_trajectory=None):
        """
        初始化分析器

        参数:
        -----
        system_trajectory: dict, optional
            系统轨迹数据，包含步骤、控制输入和响应等
        """
        self.system_trajectory = system_trajectory

    def set_trajectory(self, system_trajectory):
        """设置要分析的系统轨迹"""
        self.system_trajectory = system_trajectory

    def analyze_transient_performance(self, save_dir=None):
        """
        分析暂态性能

        参数:
        -----
        save_dir: str, optional
            图表保存目录

        返回:
        -----
        dict: 包含上升时间、稳定时间、超调量等指标
        """
        if self.system_trajectory is None or len(self.system_trajectory['steps']) == 0:
            return {'error': 'No trajectory data available'}

        # 提取轨迹数据
        steps = self.system_trajectory['steps']
        extract_purity = self.system_trajectory['extract_purity']
        raffinate_purity = self.system_trajectory['raffinate_purity']
        control_inputs = self.system_trajectory['control_inputs']

        # 检测控制输入的显著变化点
        control_changes = self._detect_control_changes(control_inputs)

        # 分析每个变化点后的响应
        response_metrics = {
            'extract': self._analyze_response(steps, extract_purity, control_changes),
            'raffinate': self._analyze_response(steps, raffinate_purity, control_changes)
        }

        # 计算平均指标
        avg_metrics = {
            'rise_time': (response_metrics['extract']['rise_time'] +
                          response_metrics['raffinate']['rise_time']) / 2,
            'settling_time': (response_metrics['extract']['settling_time'] +
                              response_metrics['raffinate']['settling_time']) / 2,
            'overshoot': (response_metrics['extract']['overshoot'] +
                          response_metrics['raffinate']['overshoot']) / 2
        }

        # 可视化暂态响应
        if save_dir:
            self._plot_transient_analysis(steps, extract_purity, raffinate_purity,
                                          control_inputs, control_changes, save_dir)

        return {
            'detailed': response_metrics,
            'average': avg_metrics
        }

    def analyze_control_efficiency(self, save_dir=None):
        """
        分析控制效率

        参数:
        -----
        save_dir: str, optional
            图表保存目录

        返回:
        -----
        dict: 包含控制信号变化频率、幅度等指标
        """
        if self.system_trajectory is None or len(self.system_trajectory['steps']) == 0:
            return {'error': 'No trajectory data available'}

        # 提取轨迹数据
        steps = self.system_trajectory['steps']
        control_inputs = self.system_trajectory['control_inputs']

        # 转换为numpy数组
        control_inputs = np.array(control_inputs)

        # 分析控制信号变化
        control_changes = np.abs(np.diff(control_inputs, axis=0))

        # 计算控制动作频率 (每步的平均变化次数)
        change_threshold = 0.0005  # 可调整的阈值
        significant_changes = (control_changes > change_threshold).sum(axis=1)
        change_frequency = significant_changes.mean()

        # 计算控制幅度
        control_amplitude = control_changes.mean(axis=0)

        # 计算控制能耗 (简化模型：与控制变化幅度平方成正比)
        control_energy = np.sum(control_changes ** 2)

        # 控制稳定性指标 (变化的方差)
        control_stability = np.var(control_changes, axis=0).mean()

        # 可视化控制效率
        if save_dir:
            self._plot_control_efficiency(steps[1:], control_changes, save_dir)

        return {
            'change_frequency': change_frequency,
            'control_amplitude': control_amplitude.tolist(),
            'control_energy': float(control_energy),
            'control_stability': float(control_stability)
        }

    def analyze_disturbance_rejection(self, disturbance_points=None, save_dir=None):
        """
        分析系统对扰动的抑制能力

        参数:
        -----
        disturbance_points: list, optional
            扰动发生的时间点列表
        save_dir: str, optional
            图表保存目录

        返回:
        -----
        dict: 包含扰动抑制率、恢复时间等指标
        """
        if self.system_trajectory is None or len(self.system_trajectory['steps']) == 0:
            return {'error': 'No trajectory data available'}

        # 提取轨迹数据
        steps = self.system_trajectory['steps']
        extract_purity = self.system_trajectory['extract_purity']
        raffinate_purity = self.system_trajectory['raffinate_purity']

        # 如果未提供扰动点，尝试自动检测
        if disturbance_points is None:
            # 检测纯度变化突变点作为潜在扰动点
            extract_changes = np.abs(np.diff(extract_purity))
            raffinate_changes = np.abs(np.diff(raffinate_purity))

            # 找出变化率突变的点
            combined_changes = extract_changes + raffinate_changes
            peaks, _ = find_peaks(combined_changes, height=np.mean(combined_changes) + 2 * np.std(combined_changes))
            disturbance_points = [steps[p] for p in peaks]

        # 如果没有检测到扰动点，返回空结果
        if not disturbance_points:
            return {'error': 'No disturbance points detected or provided'}

        # 计算每个扰动点的恢复指标
        recovery_metrics = []

        for point in disturbance_points:
            # 找到扰动点对应的索引
            point_idx = steps.index(point) if point in steps else np.argmin(np.abs(np.array(steps) - point))

            # 确保有足够的后续数据
            if point_idx + 20 >= len(steps):
                continue

            # 计算扰动前的稳态值
            pre_disturb_idx = max(0, point_idx - 10)
            pre_extract = np.mean(extract_purity[pre_disturb_idx:point_idx])
            pre_raffinate = np.mean(raffinate_purity[pre_disturb_idx:point_idx])

            # 扰动后的最大偏差
            post_idx_range = slice(point_idx, min(point_idx + 20, len(steps)))
            extract_deviation = np.max(np.abs(np.array(extract_purity[post_idx_range]) - pre_extract))
            raffinate_deviation = np.max(np.abs(np.array(raffinate_purity[post_idx_range]) - pre_raffinate))

            # 计算恢复时间
            recovery_time_extract = self._calculate_recovery_time(
                steps[point_idx:], extract_purity[point_idx:], pre_extract
            )
            recovery_time_raffinate = self._calculate_recovery_time(
                steps[point_idx:], raffinate_purity[point_idx:], pre_raffinate
            )

            recovery_metrics.append({
                'disturbance_point': point,
                'extract_deviation': extract_deviation,
                'raffinate_deviation': raffinate_deviation,
                'recovery_time_extract': recovery_time_extract,
                'recovery_time_raffinate': recovery_time_raffinate,
                'average_recovery_time': (recovery_time_extract + recovery_time_raffinate) / 2
            })

        # 计算平均指标
        if recovery_metrics:
            avg_extract_deviation = np.mean([m['extract_deviation'] for m in recovery_metrics])
            avg_raffinate_deviation = np.mean([m['raffinate_deviation'] for m in recovery_metrics])
            avg_recovery_time = np.mean([m['average_recovery_time'] for m in recovery_metrics])

            # 计算扰动抑制率 (偏差越小越好)
            disturbance_rejection_rate = 1.0 / (1.0 + avg_extract_deviation + avg_raffinate_deviation)
        else:
            avg_extract_deviation = avg_raffinate_deviation = avg_recovery_time = disturbance_rejection_rate = 0

        # 可视化扰动分析
        if save_dir and recovery_metrics:
            self._plot_disturbance_analysis(steps, extract_purity, raffinate_purity,
                                            [m['disturbance_point'] for m in recovery_metrics], save_dir)

        return {
            'detailed': recovery_metrics,
            'average': {
                'extract_deviation': avg_extract_deviation,
                'raffinate_deviation': avg_raffinate_deviation,
                'recovery_time': avg_recovery_time,
                'rejection_rate': disturbance_rejection_rate
            }
        }

    def analyze_steady_state_performance(self, save_dir=None):
        """
        分析稳态性能

        参数:
        -----
        save_dir: str, optional
            图表保存目录

        返回:
        -----
        dict: 包含稳态值、波动范围等指标
        """
        if self.system_trajectory is None or len(self.system_trajectory['steps']) == 0:
            return {'error': 'No trajectory data available'}

        # 提取轨迹数据
        steps = self.system_trajectory['steps']
        extract_purity = self.system_trajectory['extract_purity']
        raffinate_purity = self.system_trajectory['raffinate_purity']
        productivity = self.system_trajectory['productivity'] if 'productivity' in self.system_trajectory else None

        # 检测稳态区间
        steady_state_start = self._detect_steady_state(extract_purity, raffinate_purity)

        # 如果未找到稳态区间，使用后半段数据
        if steady_state_start < 0:
            steady_state_start = len(steps) // 2

        # 计算稳态区间的指标
        steady_idx = slice(steady_state_start, len(steps))
        steady_extract = np.array(extract_purity[steady_idx])
        steady_raffinate = np.array(raffinate_purity[steady_idx])

        # 计算稳态平均值和波动
        extract_mean = np.mean(steady_extract)
        extract_std = np.std(steady_extract)
        raffinate_mean = np.mean(steady_raffinate)
        raffinate_std = np.std(steady_raffinate)

        # 计算生产率指标（如果有）
        productivity_metrics = {}
        if productivity:
            steady_productivity = np.array(productivity[steady_idx])
            productivity_mean = np.mean(steady_productivity)
            productivity_std = np.std(steady_productivity)
            productivity_metrics = {
                'mean': productivity_mean,
                'std': productivity_std,
                'variation_coefficient': productivity_std / productivity_mean if productivity_mean > 0 else 0
            }

        # 计算稳态性能指标
        steady_state_metrics = {
            'steady_state_step': steps[steady_state_start],
            'extract_purity': {
                'mean': extract_mean,
                'std': extract_std,
                'variation_coefficient': extract_std / extract_mean if extract_mean > 0 else 0
            },
            'raffinate_purity': {
                'mean': raffinate_mean,
                'std': raffinate_std,
                'variation_coefficient': raffinate_std / raffinate_mean if raffinate_mean > 0 else 0
            }
        }

        if productivity_metrics:
            steady_state_metrics['productivity'] = productivity_metrics

        # 可视化稳态分析
        if save_dir:
            self._plot_steady_state_analysis(steps, extract_purity, raffinate_purity,
                                             steady_state_start, save_dir)

        return steady_state_metrics

    def generate_comprehensive_report(self, save_dir):
        """
        生成综合性能报告

        参数:
        -----
        save_dir: str
            报告保存目录

        返回:
        -----
        str: 报告文件路径
        """
        if self.system_trajectory is None or len(self.system_trajectory['steps']) == 0:
            return {'error': 'No trajectory data available'}

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 运行所有分析
        transient_metrics = self.analyze_transient_performance(save_dir)
        control_metrics = self.analyze_control_efficiency(save_dir)
        steady_state_metrics = self.analyze_steady_state_performance(save_dir)
        disturbance_metrics = self.analyze_disturbance_rejection(save_dir=save_dir)

        # 创建综合报告文件
        report_path = os.path.join(save_dir, 'process_control_report.txt')

        with open(report_path, 'w') as f:
            f.write("====== SMB过程控制性能分析报告 ======\n\n")

            # 1. 暂态性能
            f.write("1. 暂态性能指标\n")
            f.write("-" * 50 + "\n")
            if 'average' in transient_metrics:
                f.write(f"平均上升时间: {transient_metrics['average']['rise_time']:.2f} 步\n")
                f.write(f"平均稳定时间: {transient_metrics['average']['settling_time']:.2f} 步\n")
                f.write(f"平均超调量: {transient_metrics['average']['overshoot'] * 100:.2f}%\n\n")

            # 2. 控制效率
            f.write("2. 控制效率指标\n")
            f.write("-" * 50 + "\n")
            f.write(f"控制动作频率: {control_metrics['change_frequency']:.4f} 变化/步\n")
            f.write(f"控制能耗指标: {control_metrics['control_energy']:.6f}\n")
            f.write(f"控制稳定性指标: {control_metrics['control_stability']:.6f}\n\n")

            # 3. 稳态性能
            f.write("3. 稳态性能指标\n")
            f.write("-" * 50 + "\n")
            f.write(f"稳态达成时间: 第 {steady_state_metrics['steady_state_step']} 步\n")
            f.write(f"萃取纯度: {steady_state_metrics['extract_purity']['mean']:.4f} ± "
                    f"{steady_state_metrics['extract_purity']['std']:.4f} "
                    f"(变异系数: {steady_state_metrics['extract_purity']['variation_coefficient']:.4f})\n")
            f.write(f"萃余纯度: {steady_state_metrics['raffinate_purity']['mean']:.4f} ± "
                    f"{steady_state_metrics['raffinate_purity']['std']:.4f} "
                    f"(变异系数: {steady_state_metrics['raffinate_purity']['variation_coefficient']:.4f})\n")

            if 'productivity' in steady_state_metrics:
                f.write(f"生产率: {steady_state_metrics['productivity']['mean']:.6f} ± "
                        f"{steady_state_metrics['productivity']['std']:.6f} "
                        f"(变异系数: {steady_state_metrics['productivity']['variation_coefficient']:.4f})\n\n")

            # 4. 扰动抑制能力
            f.write("4. 扰动抑制指标\n")
            f.write("-" * 50 + "\n")
            if 'average' in disturbance_metrics and 'rejection_rate' in disturbance_metrics['average']:
                f.write(f"扰动抑制率: {disturbance_metrics['average']['rejection_rate']:.4f}\n")
                f.write(f"平均恢复时间: {disturbance_metrics['average']['recovery_time']:.2f} 步\n")
                f.write(f"平均偏差幅度: 萃取 {disturbance_metrics['average']['extract_deviation']:.4f}, "
                        f"萃余 {disturbance_metrics['average']['raffinate_deviation']:.4f}\n\n")

            # 5. 综合评分
            f.write("5. 综合性能评分\n")
            f.write("-" * 50 + "\n")

            # 计算简单的综合评分 (满分100)
            transient_score = 0
            steady_score = 0
            control_score = 0
            disturbance_score = 0

            # 暂态性能评分 (最高25分)
            if 'average' in transient_metrics:
                rise_time_score = max(0, 25 * (1 - transient_metrics['average']['rise_time'] / 100))
                settling_time_score = max(0, 25 * (1 - transient_metrics['average']['settling_time'] / 200))
                overshoot_score = max(0, 25 * (1 - transient_metrics['average']['overshoot'] / 0.2))
                transient_score = (rise_time_score + settling_time_score + overshoot_score) / 3

            # 稳态性能评分 (最高35分)
            extract_score = 35 * (steady_state_metrics['extract_purity']['mean'] / 0.95)
            raffinate_score = 35 * (steady_state_metrics['raffinate_purity']['mean'] / 0.95)
            variation_penalty = 35 * (steady_state_metrics['extract_purity']['variation_coefficient'] +
                                      steady_state_metrics['raffinate_purity']['variation_coefficient']) / 2
            steady_score = (extract_score + raffinate_score) / 2 - variation_penalty
            steady_score = max(0, min(35, steady_score))

            # 控制效率评分 (最高20分)
            control_score = 20 * (1 - min(1, control_metrics['control_energy'] / 0.01))

            # 扰动抑制评分 (最高20分)
            if 'average' in disturbance_metrics and 'rejection_rate' in disturbance_metrics['average']:
                disturbance_score = 20 * disturbance_metrics['average']['rejection_rate']

            # 总分
            total_score = transient_score + steady_score + control_score + disturbance_score

            f.write(f"暂态性能评分: {transient_score:.1f}/25\n")
            f.write(f"稳态性能评分: {steady_score:.1f}/35\n")
            f.write(f"控制效率评分: {control_score:.1f}/20\n")
            f.write(f"扰动抑制评分: {disturbance_score:.1f}/20\n")
            f.write(f"总评分: {total_score:.1f}/100\n\n")

            # 6. 结论和建议
            f.write("6. 结论与建议\n")
            f.write("-" * 50 + "\n")

            # 根据评分提供建议
            if transient_score < 15:
                f.write("- 暂态响应性能有待提高，可考虑优化控制参数以减少上升时间和超调。\n")

            if steady_score < 25:
                f.write("- 稳态性能不足，需要提高产品纯度并减小波动。\n")

            if control_score < 12:
                f.write("- 控制效率偏低，建议减少控制动作频率，降低能耗。\n")

            if disturbance_score < 12:
                f.write("- 扰动抑制能力不足，系统鲁棒性需要加强。\n")

            if total_score >= 80:
                f.write("- 总体表现优异，系统控制性能达到高水平。\n")
            elif total_score >= 60:
                f.write("- 总体表现良好，但仍有提升空间。\n")
            else:
                f.write("- 系统性能有显著提升空间，建议重新调整控制策略。\n")

        # 创建性能雷达图
        self._plot_performance_radar(
            [transient_score / 25, steady_score / 35, control_score / 20, disturbance_score / 20],
            save_dir
        )

        return report_path

    # 内部辅助方法
    def _detect_control_changes(self, control_inputs, threshold=0.001):
        """检测控制输入的显著变化点"""
        if not control_inputs or len(control_inputs) < 2:
            return []

        control_array = np.array(control_inputs)
        changes = np.abs(np.diff(control_array, axis=0))
        change_sum = np.sum(changes, axis=1)

        # 找出变化大于阈值的索引
        change_points = np.where(change_sum > threshold)[0]

        # 过滤太接近的变化点
        filtered_points = []
        for point in change_points:
            if not filtered_points or point - filtered_points[-1] > 5:  # 间隔至少5步
                filtered_points.append(point)

        return filtered_points

    def _analyze_response(self, steps, response, change_points, target_offset=0.05):
        """分析响应特性"""
        if not change_points:
            return {
                'rise_time': 0,
                'settling_time': 0,
                'overshoot': 0
            }

        # 分析所有变化点后的响应
        rise_times = []
        settling_times = []
        overshoots = []

        for change_idx in change_points:
            if change_idx + 2 >= len(steps):
                continue

            # 变化前的稳态值
            pre_change_idx = max(0, change_idx - 5)
            pre_value = np.mean(response[pre_change_idx:change_idx])

            # 响应后的最大值索引
            post_idx_range = range(change_idx + 1, min(change_idx + 30, len(steps)))
            if not post_idx_range:
                continue

            post_values = [response[i] for i in post_idx_range]
            if not post_values:
                continue

            max_idx = post_idx_range[np.argmax(np.abs(np.array(post_values) - pre_value))]
            max_value = response[max_idx]

            # 目标值 = 最终稳态值
            final_idx_range = range(min(change_idx + 20, len(steps) - 10), min(change_idx + 30, len(steps)))
            if not final_idx_range:
                continue

            final_values = [response[i] for i in final_idx_range]
            if not final_values:
                continue

            target_value = np.mean(final_values)

            # 计算上升时间 (从变化点到达目标值90%的时间)
            target_90 = pre_value + 0.9 * (target_value - pre_value)
            for i in range(change_idx + 1, min(change_idx + 30, len(steps))):
                if abs(response[i] - target_90) <= target_offset * abs(target_value - pre_value):
                    rise_times.append(i - change_idx)
                    break

            # 计算稳定时间 (从变化点到持续在目标值5%范围内的时间)
            for i in range(change_idx + 1, min(change_idx + 50, len(steps) - 5)):
                window = response[i:i + 5]
                if all(abs(v - target_value) <= target_offset * abs(target_value - pre_value) for v in window):
                    settling_times.append(i - change_idx)
                    break

            # 计算超调量
            if abs(target_value - pre_value) > 0.001:  # 避免除零
                overshoot = abs(max_value - target_value) / abs(target_value - pre_value)
                overshoots.append(overshoot)

        # 计算平均值
        if rise_times:
            avg_rise_time = np.mean(rise_times)
        else:
            avg_rise_time = 0

        if settling_times:
            avg_settling_time = np.mean(settling_times)
        else:
            avg_settling_time = 0

        if overshoots:
            avg_overshoot = np.mean(overshoots)
        else:
            avg_overshoot = 0

        return {
            'rise_time': avg_rise_time,
            'settling_time': avg_settling_time,
            'overshoot': avg_overshoot
        }

    def _calculate_recovery_time(self, steps, values, target, threshold=0.05):
        """计算从扰动恢复到目标值的时间"""
        if not steps or not values:
            return 0

        # 扰动后的恢复时间 (回到目标值5%范围内)
        for i in range(1, len(steps)):
            if abs(values[i] - target) <= threshold * target:
                return steps[i] - steps[0]

        return steps[-1] - steps[0]  # 如果没有恢复，返回总时长

    def _detect_steady_state(self, extract_purity, raffinate_purity, window_size=20, threshold=0.005):
        """检测稳态开始点"""
        if len(extract_purity) < window_size * 2:
            return -1

        # 计算移动标准差
        extract_std = []
        raffinate_std = []

        for i in range(len(extract_purity) - window_size + 1):
            extract_window = extract_purity[i:i + window_size]
            raffinate_window = raffinate_purity[i:i + window_size]

            extract_std.append(np.std(extract_window))
            raffinate_std.append(np.std(raffinate_window))

        # 找到两个纯度同时变得稳定的点
        for i in range(len(extract_std)):
            if (extract_std[i] < threshold * np.mean(extract_purity[i:i + window_size]) and
                    raffinate_std[i] < threshold * np.mean(raffinate_purity[i:i + window_size])):
                return i + window_size // 2  # 返回窗口中间点

        return -1

    def _plot_transient_analysis(self, steps, extract, raffinate, controls, change_points, save_dir):
        """绘制暂态分析图"""
        # 设置绘图风格
        setup_publication_style()

        plt.figure(figsize=(12, 8))

        # 绘制纯度曲线
        plt.subplot(2, 1, 1)
        plt.plot(steps, extract, label='Extract Purity', linewidth=2)
        plt.plot(steps, raffinate, label='Raffinate Purity', linewidth=2)

        # 标记控制变化点
        for cp in change_points:
            if cp < len(steps):
                plt.axvline(x=steps[cp], color='red', linestyle='--', alpha=0.7)

        plt.xlabel('Steps', fontweight='bold')
        plt.ylabel('Purity', fontweight='bold')
        plt.title('Transient Response Analysis', fontweight='bold')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        # 绘制控制信号
        plt.subplot(2, 1, 2)
        controls_array = np.array(controls)
        for i in range(controls_array.shape[1]):
            plt.plot(steps, controls_array[:, i], label=f'Control {i + 1}')

        # 标记控制变化点
        for cp in change_points:
            if cp < len(steps):
                plt.axvline(x=steps[cp], color='red', linestyle='--', alpha=0.7)

        plt.xlabel('Steps', fontweight='bold')
        plt.ylabel('Control Value', fontweight='bold')
        plt.title('Control Signals', fontweight='bold')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'transient_analysis.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(save_dir, 'transient_analysis.pdf'), format='pdf', bbox_inches='tight')
        plt.close()

    def _plot_control_efficiency(self, steps, control_changes, save_dir):
        """绘制控制效率分析图"""
        # 设置绘图风格
        setup_publication_style()

        plt.figure(figsize=(12, 6))

        # 计算每步总变化幅度
        total_changes = np.sum(control_changes, axis=1)

        # 绘制控制变化幅度
        plt.plot(steps, total_changes, label='Control Change Magnitude', linewidth=2)

        # 添加移动平均线
        window_size = min(10, len(total_changes))
        if window_size > 1:
            moving_avg = np.convolve(total_changes, np.ones(window_size) / window_size, mode='valid')
            plt.plot(steps[window_size - 1:], moving_avg,
                     label=f'Moving Average ({window_size} steps)', linewidth=2)

        plt.xlabel('Steps', fontweight='bold')
        plt.ylabel('Control Change Magnitude', fontweight='bold')
        plt.title('Control Efficiency Analysis', fontweight='bold')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'control_efficiency.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(save_dir, 'control_efficiency.pdf'), format='pdf', bbox_inches='tight')
        plt.close()

    def _plot_disturbance_analysis(self, steps, extract, raffinate, disturbance_points, save_dir):
        """绘制扰动分析图"""
        # 设置绘图风格
        setup_publication_style()

        plt.figure(figsize=(12, 6))

        # 绘制纯度曲线
        plt.plot(steps, extract, label='Extract Purity', linewidth=2)
        plt.plot(steps, raffinate, label='Raffinate Purity', linewidth=2)

        # 标记扰动点
        for dp in disturbance_points:
            plt.axvline(x=dp, color='red', linestyle='--', alpha=0.7,
                        label='Disturbance' if dp == disturbance_points[0] else None)

        plt.xlabel('Steps', fontweight='bold')
        plt.ylabel('Purity', fontweight='bold')
        plt.title('Disturbance Rejection Analysis', fontweight='bold')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'disturbance_analysis.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(save_dir, 'disturbance_analysis.pdf'), format='pdf', bbox_inches='tight')
        plt.close()

    def _plot_steady_state_analysis(self, steps, extract, raffinate, steady_start_idx, save_dir):
        """绘制稳态分析图"""
        # 设置绘图风格
        setup_publication_style()

        plt.figure(figsize=(12, 6))

        # 绘制纯度曲线
        plt.plot(steps, extract, label='Extract Purity', linewidth=2)
        plt.plot(steps, raffinate, label='Raffinate Purity', linewidth=2)

        # 标记稳态起始点
        if steady_start_idx >= 0 and steady_start_idx < len(steps):
            plt.axvline(x=steps[steady_start_idx], color='green', linestyle='--',
                        label=f'Steady State (step {steps[steady_start_idx]})')

            # 添加稳态区域阴影
            plt.axvspan(steps[steady_start_idx], steps[-1], alpha=0.1, color='green')

        plt.xlabel('Steps', fontweight='bold')
        plt.ylabel('Purity', fontweight='bold')
        plt.title('Steady State Analysis', fontweight='bold')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'steady_state_analysis.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(save_dir, 'steady_state_analysis.pdf'), format='pdf', bbox_inches='tight')
        plt.close()

    def _plot_performance_radar(self, scores, save_dir):
        """绘制性能雷达图"""
        # 设置绘图风格
        setup_publication_style()

        # 创建雷达图
        fig = plt.figure(figsize=(8, 8))
        ax = fig.add_subplot(111, polar=True)

        # 定义雷达图属性
        categories = ['Transient performance', 'Steady-state performance', 'Control efficiency', 'Disturbance suppression']

        # 确保评分范围为0-1
        scores = [min(1, max(0, score)) for score in scores]

        # 闭合的多边形
        values = scores + [scores[0]]
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += [angles[0]]

        # 绘制雷达图
        ax.plot(angles, values, linewidth=2, linestyle='solid')
        ax.fill(angles, values, alpha=0.25)

        # 设置刻度标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)

        # 设置雷达图样式
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
        ax.set_ylim(0, 1)

        plt.title('Process Control Performance Profile', fontweight='bold', size=16, pad=20)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'performance_radar.png'), dpi=300, bbox_inches='tight')
        plt.savefig(os.path.join(save_dir, 'performance_radar.pdf'), format='pdf', bbox_inches='tight')
        plt.close()