import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Any, Optional
import torch
from torch_geometric.data import Data


class SMBGraphConstructor:
    """
    SMB图构建器

    根据SMB环境的状态动态构建图结构，支持多种图构建策略
    作为算法层的前处理模块，独立于环境

    参数:
    -----
    construction_strategy: str
        图构建策略，可选:
        - 'physical_topology': 基于物理拓扑的图构建
        - 'functional_relation': 基于功能关系的图构建
        - 'spatiotemporal': 结合时空信息的动态图构建
    temporal_window: int
        时序窗口大小，用于时空融合策略，默认为5
    """

    def __init__(self, construction_strategy='physical_topology', temporal_window=5):
        self.strategy = construction_strategy
        self.temporal_window = temporal_window

        # 存储历史状态数据
        self.state_history = []

        # 节点和边特征模板
        self.node_features_dim = {
            'column': 5,  # 浓度A/B, 出口浓度A/B, 角色
            'phase': 4,  # 液相浓度A/B, 固相浓度A/B
            'port': 3,  # 浓度A/B, 流速
            'agent': 3,  # 控制值, 主要区域, 次要区域
            'performance': 1  # 性能指标值
        }

        self.edge_features_dim = {
            'flow': 3,  # 流速, 浓度A差, 浓度B差
            'contains': 1,  # 关系强度
            'control': 2,  # 控制强度, 优先级
            'monitor': 2,  # 监控强度, 优先级
            'metric': 1  # 关系强度
        }

        # 初始化临时图结构
        self.G = None

    def construct_graph(self, state):
        """
        根据SMB状态构建图结构

        参数:
        -----
        state: Dict
            SMB环境返回的状态字典

        返回:
        -----
        graph_data: torch_geometric.data.Data
            PyTorch Geometric格式的图数据
        """
        # 保存历史状态
        self.state_history.append(state)
        if len(self.state_history) > self.temporal_window:
            self.state_history.pop(0)

        # 根据策略选择构建方法
        if self.strategy == 'physical_topology':
            return self._construct_physical_topology(state)
        elif self.strategy == 'functional_relation':
            return self._construct_functional_relation(state)
        elif self.strategy == 'spatiotemporal':
            return self._construct_spatiotemporal_graph(state)
        else:
            raise ValueError(f"未知的图构建策略: {self.strategy}")

    def _construct_physical_topology(self, state):
        """
        基于物理拓扑构建图

        将SMB系统建模为物理连接的组件网络:
        - 柱节点: 代表色谱柱(液相和固相)
        - 端口节点: 代表萃取口、萃余口、进料口和脱附剂口
        - 流动边: 代表物质在节点间的流动
        """
        # 尝试提取原始状态数据，如果不存在则使用状态本身
        raw_state = state.get('raw_state', state)

        # 尝试从不同的键获取所需数据
        if 'current_state' in raw_state:
            current_state = raw_state['current_state']
        else:
            # 如果没有current_state，创建一个简化版本
            current_state = {
                'columns_data': [],  # 默认空列数据
                'port_positions': {
                    'extract': 1,  # 默认位置
                    'raffinate': 5,
                    'feed': 4,
                    'desorbent': 0
                }
            }
            # 尝试从状态中提取列数据
            if 'columns' in raw_state:
                current_state['columns_data'] = raw_state['columns']

        # 获取列数据和端口位置
        columns_data = current_state.get('columns_data', [])
        port_positions = current_state.get('port_positions', {
            'extract': 1, 'raffinate': 5, 'feed': 4, 'desorbent': 0
        })

        # 检查列数据是否为空，如果为空则创建默认数据
        if not columns_data:
            # 创建8个默认列
            for i in range(8):
                columns_data.append({
                    'column_id': i,
                    'role': i,
                    'avg_concentration': {
                        'fluid_A': 0.1,
                        'fluid_B': 0.1,
                        'solid_A': 0.05,
                        'solid_B': 0.05
                    },
                    'exit_concentration': {
                        'fluid_A': 0.1,
                        'fluid_B': 0.1
                    }
                })

        # 获取控制输入，如果不存在则使用默认值
        control_input = state.get('control_input', raw_state.get('control_input', [0.013, 0.013, 0.014, 0.014]))

        # 获取浓度数据，默认值为[0.1, 0.1]
        extract_conc = state.get('extract_conc', raw_state.get('extract_conc', [0.1, 0.1]))
        raffinate_conc = state.get('raffinate_conc', raw_state.get('raffinate_conc', [0.1, 0.1]))

        # 获取模式，默认为0
        mode = state.get('mode', raw_state.get('mode', 0))

        # 获取纯度指标，默认为0.5
        extract_purity = raw_state.get('extract_purity', 0.5)
        raffinate_purity = raw_state.get('raffinate_purity', 0.5)

        # 获取进料浓度，默认为{'A': 0.5, 'B': 0.5}
        feed_concentration = raw_state.get('feed_concentration', {'A': 0.5, 'B': 0.5})

        # 确保feed_concentration具有正确的格式
        if isinstance(feed_concentration, list):
            feed_concentration = {'A': feed_concentration[0], 'B': feed_concentration[1]}
        elif not isinstance(feed_concentration, dict):
            feed_concentration = {'A': 0.5, 'B': 0.5}

        # 初始化图
        G = nx.DiGraph()

        # 1. 添加柱节点
        for i, col_data in enumerate(columns_data):
            # 确保列数据具有所有必要的键
            avg_conc = col_data.get('avg_concentration',
                                    {'fluid_A': 0.1, 'fluid_B': 0.1, 'solid_A': 0.05, 'solid_B': 0.05})
            exit_conc = col_data.get('exit_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})
            role = col_data.get('role', i)

            # 确保浓度字典有必要的键
            if not all(k in avg_conc for k in ['fluid_A', 'fluid_B', 'solid_A', 'solid_B']):
                avg_conc = {'fluid_A': 0.1, 'fluid_B': 0.1, 'solid_A': 0.05, 'solid_B': 0.05}
            if not all(k in exit_conc for k in ['fluid_A', 'fluid_B']):
                exit_conc = {'fluid_A': 0.1, 'fluid_B': 0.1}

            # 柱节点
            node_id = f'column_{i}'
            G.add_node(node_id,
                       type='column',
                       features=np.array([
                           avg_conc['fluid_A'],
                           avg_conc['fluid_B'],
                           exit_conc['fluid_A'],
                           exit_conc['fluid_B'],
                           role
                       ], dtype=np.float32),
                       column_index=i,
                       role=role)

            # 相节点 (每列一个)
            phase_id = f'phase_{i}'
            G.add_node(phase_id,
                       type='phase',
                       features=np.array([
                           avg_conc['fluid_A'],
                           avg_conc['fluid_B'],
                           avg_conc['solid_A'],
                           avg_conc['solid_B']
                       ], dtype=np.float32),
                       column_index=i)

            # 添加柱和相之间的关系
            G.add_edge(node_id, phase_id,
                       type='contains',
                       features=np.array([1.0], dtype=np.float32))

        # 2. 添加柱之间的流动关系
        for i in range(8):
            next_i = (i + 1) % 8
            # 计算流速和浓度差异
            col_data = columns_data[i]
            next_col_data = columns_data[next_i]

            # 估计列间流速 (基于该列角色)
            flow_rate = self._estimate_flow_rate(i, control_input, mode)

            # 获取当前列和下一列的出口浓度
            curr_exit_conc = col_data.get('exit_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})
            next_avg_conc = next_col_data.get('avg_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})

            # 计算浓度差异
            conc_A_diff = next_avg_conc.get('fluid_A', 0.1) - curr_exit_conc.get('fluid_A', 0.1)
            conc_B_diff = next_avg_conc.get('fluid_B', 0.1) - curr_exit_conc.get('fluid_B', 0.1)

            # 添加流动边
            G.add_edge(f'column_{i}', f'column_{next_i}',
                       type='flow',
                       features=np.array([flow_rate, conc_A_diff, conc_B_diff], dtype=np.float32))

        # 3. 添加端口节点
        # 提取口
        extract_col = port_positions['extract']
        G.add_node('extract',
                   type='port',
                   features=np.array([
                       extract_conc[0] if isinstance(extract_conc, list) and len(extract_conc) > 0 else 0.1,
                       extract_conc[1] if isinstance(extract_conc, list) and len(extract_conc) > 1 else 0.1,
                       control_input[1] if isinstance(control_input, list) and len(control_input) > 1 else 0.013
                   ], dtype=np.float32))

        # 萃余口
        raffinate_col = port_positions['raffinate']
        G.add_node('raffinate',
                   type='port',
                   features=np.array([
                       raffinate_conc[0] if isinstance(raffinate_conc, list) and len(raffinate_conc) > 0 else 0.1,
                       raffinate_conc[1] if isinstance(raffinate_conc, list) and len(raffinate_conc) > 1 else 0.1,
                       control_input[2] if isinstance(control_input, list) and len(control_input) > 2 else 0.014
                   ], dtype=np.float32))

        # 进料口
        feed_col = port_positions['feed']
        feed_conc_a = feed_concentration.get('A', 0.5)
        feed_conc_b = feed_concentration.get('B', 0.5)
        feed_flow = (control_input[3] - control_input[2]) if (
                    isinstance(control_input, list) and len(control_input) > 3) else 0.001

        G.add_node('feed',
                   type='port',
                   features=np.array([
                       feed_conc_a,
                       feed_conc_b,
                       feed_flow
                   ], dtype=np.float32))

        # 脱附剂口
        desorbent_col = port_positions['desorbent']
        G.add_node('desorbent',
                   type='port',
                   features=np.array([
                       0.0,  # 脱附剂中A的浓度
                       0.0,  # 脱附剂中B的浓度
                       control_input[0] if isinstance(control_input, list) and len(control_input) > 0 else 0.013
                   ], dtype=np.float32))

        # 添加端口与柱的连接
        section1_flow = control_input[0] if isinstance(control_input, list) and len(control_input) > 0 else 0.013
        section2_flow = control_input[1] if isinstance(control_input, list) and len(control_input) > 1 else 0.013
        section3_flow = control_input[2] if isinstance(control_input, list) and len(control_input) > 2 else 0.014
        section4_flow = control_input[3] if isinstance(control_input, list) and len(control_input) > 3 else 0.014

        G.add_edge(f'column_{feed_col}', 'feed',
                   type='flow',
                   features=np.array([section4_flow - section3_flow, 0.0, 0.0], dtype=np.float32))

        G.add_edge('desorbent', f'column_{desorbent_col}',
                   type='flow',
                   features=np.array([section1_flow, 0.0, 0.0], dtype=np.float32))

        G.add_edge(f'column_{extract_col}', 'extract',
                   type='flow',
                   features=np.array([section2_flow, 0.0, 0.0], dtype=np.float32))

        G.add_edge(f'column_{raffinate_col}', 'raffinate',
                   type='flow',
                   features=np.array([section3_flow, 0.0, 0.0], dtype=np.float32))

        # 4. 添加性能指标节点
        G.add_node('extract_purity',
                   type='performance',
                   features=np.array([extract_purity], dtype=np.float32))

        G.add_node('raffinate_purity',
                   type='performance',
                   features=np.array([raffinate_purity], dtype=np.float32))

        # 添加性能指标与端口的连接
        G.add_edge('extract', 'extract_purity',
                   type='metric',
                   features=np.array([1.0], dtype=np.float32))

        G.add_edge('raffinate', 'raffinate_purity',
                   type='metric',
                   features=np.array([1.0], dtype=np.float32))

        # 保存图
        self.G = G

        # 转换为PyTorch Geometric格式
        return self._convert_to_pyg(G)

    def _construct_functional_relation(self, state):
        """
        基于功能关系构建图

        将SMB系统建模为功能关系网络:
        - 添加智能体节点和控制关系
        - 基于功能将色谱柱分组
        - 添加与区段功能相关的边
        """
        # 首先构建基本物理拓扑
        # 这会创建一个新的self.G
        pyg_data = self._construct_physical_topology(state)
        G = self.G

        # 提取原始状态数据，如果不存在则使用状态本身
        raw_state = state.get('raw_state', state)

        # 提取各种必要的数据，为缺失值提供默认值
        current_state = raw_state.get('current_state', {})
        columns_data = current_state.get('columns_data', [])
        control_input = state.get('control_input', raw_state.get('control_input', [0.013, 0.013, 0.014, 0.014]))
        mode = state.get('mode', raw_state.get('mode', 0))

        # 检查列数据是否为空
        if not columns_data:
            # 已在physical_topology中创建默认列数据，这里可以直接使用G中的数据
            pass

        # 1. 添加区段功能标签
        # 基于当前模式识别每列所属的区段
        for i in range(8):
            if f'column_{i}' in G.nodes:
                col_role = (i - mode) % 8
                section = self._map_role_to_section(col_role)
                G.nodes[f'column_{i}']['section'] = section

        # 2. 添加智能体节点
        if 'region_priority' in state:
            # 假设使用control_based模式
            region_priority = state['region_priority']
            primary_columns = region_priority.get('primary', [0, 1])
            secondary_columns = region_priority.get('secondary', [2, 3, 4, 5, 6, 7])

            # 判断使用哪种智能体模式
            agent_ids = state.get('agent_ids', [])
            if 'agent_0' in agent_ids or len(primary_columns) <= 2:
                # control_based模式
                for i in range(4):  # 4个智能体
                    agent_id = f'agent_{i}'

                    # 计算主要和次要区域
                    if i < len(primary_columns) // 2:
                        primary = primary_columns[i * 2:(i + 1) * 2]
                    else:
                        primary = []

                    # 确保控制输入有足够的元素
                    agent_control = control_input[i] if i < len(control_input) else 0.013

                    # 添加智能体节点
                    G.add_node(agent_id,
                               type='agent',
                               features=np.array([
                                   agent_control,  # 控制值
                                   i,  # 智能体索引
                                   len(primary)  # 主要区域大小
                               ], dtype=np.float32))

                    # 添加控制关系 - 主要区域为强控制关系
                    for col in primary:
                        if f'column_{col}' in G.nodes:
                            G.add_edge(agent_id, f'column_{col}',
                                       type='control',
                                       features=np.array([1.0, 1.0], dtype=np.float32))

                    # 添加监控关系 - 次要区域为弱监控关系
                    for col in secondary_columns:
                        if col not in primary and f'column_{col}' in G.nodes:
                            G.add_edge(agent_id, f'column_{col}',
                                       type='monitor',
                                       features=np.array([0.5, 0.5], dtype=np.float32))
            else:
                # function_based模式
                # 确保控制输入有足够的元素
                ctrl_0 = control_input[0] if len(control_input) > 0 else 0.013
                ctrl_1 = control_input[1] if len(control_input) > 1 else 0.013
                ctrl_2 = control_input[2] if len(control_input) > 2 else 0.014
                ctrl_3 = control_input[3] if len(control_input) > 3 else 0.014

                # 萃取智能体
                G.add_node('extract_agent',
                           type='agent',
                           features=np.array([
                               (ctrl_0 + ctrl_1) / 2,  # 平均控制值
                               0,  # 智能体类型(0=萃取)
                               len(primary_columns)  # 主要区域大小
                           ], dtype=np.float32))

                # 萃余智能体
                G.add_node('raffinate_agent',
                           type='agent',
                           features=np.array([
                               (ctrl_2 + ctrl_3) / 2,  # 平均控制值
                               1,  # 智能体类型(1=萃余)
                               len(secondary_columns)  # 主要区域大小
                           ], dtype=np.float32))

                # 添加萃取智能体的控制和监控关系
                for col in range(8):
                    if f'column_{col}' in G.nodes:
                        if col in primary_columns:
                            G.add_edge('extract_agent', f'column_{col}',
                                       type='control',
                                       features=np.array([1.0, 1.0], dtype=np.float32))
                        else:
                            G.add_edge('extract_agent', f'column_{col}',
                                       type='monitor',
                                       features=np.array([0.5, 0.5], dtype=np.float32))

                # 添加萃余智能体的控制和监控关系
                for col in range(8):
                    if f'column_{col}' in G.nodes:
                        if col in secondary_columns:
                            G.add_edge('raffinate_agent', f'column_{col}',
                                       type='control',
                                       features=np.array([1.0, 1.0], dtype=np.float32))
                        else:
                            G.add_edge('raffinate_agent', f'column_{col}',
                                       type='monitor',
                                       features=np.array([0.5, 0.5], dtype=np.float32))

        # 3. 添加基于功能的额外边
        # 连接同一区段的柱节点 - 根据功能分组
        for section in range(1, 5):  # 4个区段
            # 找出属于同一区段的列
            section_columns = []
            for i in range(8):
                col_node = f'column_{i}'
                if col_node in G.nodes and G.nodes[col_node].get('section', -1) == section:
                    section_columns.append(i)

            # 在同一区段的列之间添加功能关联边
            for i, col1 in enumerate(section_columns):
                for col2 in section_columns[i + 1:]:
                    col1_node = f'column_{col1}'
                    col2_node = f'column_{col2}'
                    if col1_node in G.nodes and col2_node in G.nodes:
                        G.add_edge(col1_node, col2_node,
                                   type='functional',
                                   features=np.array([
                                       1.0,  # 关系强度
                                       abs(col1 - col2),  # 物理距离
                                       0.0  # 预留
                                   ], dtype=np.float32))

                        # 双向连接
                        G.add_edge(col2_node, col1_node,
                                   type='functional',
                                   features=np.array([
                                       1.0,  # 关系强度
                                       abs(col1 - col2),  # 物理距离
                                       0.0  # 预留
                                   ], dtype=np.float32))

        # 4. 连接性能指标和智能体
        # 萃取纯度与萃取相关智能体
        if all(f'agent_{i}' in G.nodes for i in range(4)):
            if 'extract_purity' in G.nodes and 'agent_0' in G.nodes and 'agent_1' in G.nodes:
                G.add_edge('extract_purity', 'agent_0',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))
                G.add_edge('extract_purity', 'agent_1',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))

            if 'raffinate_purity' in G.nodes and 'agent_2' in G.nodes and 'agent_3' in G.nodes:
                G.add_edge('raffinate_purity', 'agent_2',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))
                G.add_edge('raffinate_purity', 'agent_3',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))
        elif 'extract_agent' in G.nodes and 'raffinate_agent' in G.nodes:
            if 'extract_purity' in G.nodes:
                G.add_edge('extract_purity', 'extract_agent',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))
            if 'raffinate_purity' in G.nodes:
                G.add_edge('raffinate_purity', 'raffinate_agent',
                           type='metric',
                           features=np.array([1.0], dtype=np.float32))

        # 更新图
        self.G = G

        # 转换为PyTorch Geometric格式
        return self._convert_to_pyg(G)

    def _construct_spatiotemporal_graph(self, state):
        """
        结合时空信息构建动态图

        将SMB系统建模为时空动态网络:
        - 包含时序特征
        - 添加动态边权重
        - 使用历史状态捕捉系统动态性
        """
        # 首先构建功能关系图 - 这包含了基本拓扑和功能关系
        pyg_data = self._construct_functional_relation(state)
        G = self.G  # 这是功能图

        # 如果历史状态不足，无法构建时空图，则返回功能图
        if len(self.state_history) < 2:
            return pyg_data

        # 获取当前状态数据
        raw_state = state.get('raw_state', state)
        current_state = raw_state.get('current_state', {})
        mode = state.get('mode', raw_state.get('mode', 0))
        step_count = raw_state.get('step_count', 0)

        # 提取当前工况信息 (如果有)
        current_workload = raw_state.get('current_workload', 'normal')

        # 提取历史状态
        historical_states = self.state_history[:-1]  # 不包括当前状态

        # 1. 为每个节点添加时序特征
        for node in G.nodes:
            node_type = G.nodes[node].get('type', '')

            # 跳过不需要时序特征的节点
            if node_type not in ['column', 'phase', 'port']:
                continue

            # 初始化时序特征列表
            temporal_features = []

            # 对于每个历史状态，提取相应节点的特征
            for hist_state in reversed(historical_states):  # 从最近到最远
                # 获取历史状态数据
                hist_raw_state = hist_state.get('raw_state', hist_state)
                hist_current_state = hist_raw_state.get('current_state', {})

                # 尝试从历史状态中提取特征
                if node_type == 'column':
                    # 提取列索引
                    if node.startswith('column_'):
                        try:
                            col_idx = int(node.split('_')[1])
                        except (IndexError, ValueError):
                            continue

                        # 尝试提取历史列数据
                        hist_columns = hist_current_state.get('columns_data', [])
                        if col_idx < len(hist_columns):
                            hist_col_data = hist_columns[col_idx]

                            # 确保列数据包含必要的键
                            avg_conc = hist_col_data.get('avg_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})
                            exit_conc = hist_col_data.get('exit_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})
                            role = hist_col_data.get('role', col_idx)

                            # 提取与当前特征相同格式的历史特征
                            hist_feature = np.array([
                                avg_conc.get('fluid_A', 0.1),
                                avg_conc.get('fluid_B', 0.1),
                                exit_conc.get('fluid_A', 0.1),
                                exit_conc.get('fluid_B', 0.1),
                                role
                            ], dtype=np.float32)

                            temporal_features.append(hist_feature)

                elif node_type == 'port':
                    # 处理端口节点
                    if node == 'extract':
                        # 获取历史萃取浓度和控制输入
                        hist_extract_conc = hist_state.get('extract_conc', [0.1, 0.1])
                        hist_control_input = hist_state.get('control_input', [0.013, 0.013, 0.014, 0.014])

                        # 确保数据是列表且有足够的元素
                        if not isinstance(hist_extract_conc, list):
                            hist_extract_conc = [0.1, 0.1]
                        if len(hist_extract_conc) < 2:
                            hist_extract_conc = [0.1, 0.1]

                        if not isinstance(hist_control_input, list):
                            hist_control_input = [0.013, 0.013, 0.014, 0.014]
                        if len(hist_control_input) < 2:
                            hist_control_input = [0.013, 0.013, 0.014, 0.014]

                        hist_feature = np.array([
                            hist_extract_conc[0],
                            hist_extract_conc[1],
                            hist_control_input[1]  # 区段2流速
                        ], dtype=np.float32)
                        temporal_features.append(hist_feature)

                    elif node == 'raffinate':
                        # 获取历史萃余浓度和控制输入
                        hist_raffinate_conc = hist_state.get('raffinate_conc', [0.1, 0.1])
                        hist_control_input = hist_state.get('control_input', [0.013, 0.013, 0.014, 0.014])

                        # 确保数据是列表且有足够的元素
                        if not isinstance(hist_raffinate_conc, list):
                            hist_raffinate_conc = [0.1, 0.1]
                        if len(hist_raffinate_conc) < 2:
                            hist_raffinate_conc = [0.1, 0.1]

                        if not isinstance(hist_control_input, list):
                            hist_control_input = [0.013, 0.013, 0.014, 0.014]
                        if len(hist_control_input) < 3:
                            hist_control_input = [0.013, 0.013, 0.014, 0.014]

                        hist_feature = np.array([
                            hist_raffinate_conc[0],
                            hist_raffinate_conc[1],
                            hist_control_input[2]  # 区段3流速
                        ], dtype=np.float32)
                        temporal_features.append(hist_feature)

            # 如果成功提取了历史特征，添加到节点
            if temporal_features:
                # 将特征列表填充到指定窗口大小
                while len(temporal_features) < self.temporal_window - 1:
                    # 使用第一个历史特征填充
                    if temporal_features:
                        temporal_features.append(temporal_features[0])
                    else:
                        # 如果没有历史特征，使用当前特征填充
                        if 'features' in G.nodes[node]:
                            temporal_features.append(G.nodes[node]['features'])
                        else:
                            # 创建一个默认特征
                            default_feature = np.zeros(5 if node_type == 'column' else 3, dtype=np.float32)
                            temporal_features.append(default_feature)

                # 添加时序特征到节点
                G.nodes[node]['temporal_features'] = temporal_features

        # 2. 添加动态边权重
        # 基于历史状态计算边的动态特性
        if len(historical_states) >= 2:
            prev_state = historical_states[-1]
            prev_raw_state = prev_state.get('raw_state', prev_state)
            prev_current_state = prev_raw_state.get('current_state', {})

            # 处理流动边
            for i in range(8):
                next_i = (i + 1) % 8
                edge = (f'column_{i}', f'column_{next_i}')

                if edge in G.edges:
                    # 获取当前和历史列数据
                    curr_columns = current_state.get('columns_data', [])
                    prev_columns = prev_current_state.get('columns_data', [])

                    # 确保有足够的列数据
                    if i < len(curr_columns) and next_i < len(curr_columns) and i < len(prev_columns) and next_i < len(
                            prev_columns):
                        # 获取当前和历史浓度数据
                        curr_col = curr_columns[i]
                        prev_col = prev_columns[i]

                        # 确保有必要的键
                        curr_exit_conc = curr_col.get('exit_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})
                        prev_exit_conc = prev_col.get('exit_concentration', {'fluid_A': 0.1, 'fluid_B': 0.1})

                        # 计算浓度变化率
                        curr_conc_A = curr_exit_conc.get('fluid_A', 0.1)
                        prev_conc_A = prev_exit_conc.get('fluid_A', 0.1)
                        conc_A_change_rate = (curr_conc_A - prev_conc_A) / max(0.001, prev_conc_A)

                        curr_conc_B = curr_exit_conc.get('fluid_B', 0.1)
                        prev_conc_B = prev_exit_conc.get('fluid_B', 0.1)
                        conc_B_change_rate = (curr_conc_B - prev_conc_B) / max(0.001, prev_conc_B)

                        # 添加动态特征到边
                        G.edges[edge]['dynamic_features'] = np.array([
                            conc_A_change_rate,
                            conc_B_change_rate,
                            0.0  # 预留
                        ], dtype=np.float32)

        # 3. 添加时空关系边
        # 连接系统中的时态变化
        if len(historical_states) >= 1:
            # 添加时间步节点
            G.add_node('time_step',
                       type='time',
                       features=np.array([
                           step_count,
                           mode,
                           0.0  # 预留
                       ], dtype=np.float32))

            # 连接时间步与所有列节点
            for i in range(8):
                col_node = f'column_{i}'
                if col_node in G.nodes:
                    G.add_edge('time_step', col_node,
                               type='temporal',
                               features=np.array([
                                   1.0,  # 关系强度
                                   0.0  # 预留
                               ], dtype=np.float32))

            # 添加周期性端口切换信息
            port_switch_interval = 4  # 假设每4步切换一次
            port_switch_progress = step_count % port_switch_interval / port_switch_interval

            # 添加端口切换节点
            G.add_node('port_switch',
                       type='event',
                       features=np.array([
                           port_switch_progress,  # 切换进度(0-1)
                           mode,
                           0.0  # 预留
                       ], dtype=np.float32))

            # 连接端口切换与所有端口节点
            for port in ['extract', 'raffinate', 'feed', 'desorbent']:
                if port in G.nodes:
                    G.add_edge('port_switch', port,
                               type='event',
                               features=np.array([
                                   1.0 - port_switch_progress,  # 关系强度(切换即将发生时更强)
                                   0.0  # 预留
                               ], dtype=np.float32))

        # 4. 新增: 添加工况信息节点
        G.add_node('workload_info',
                   type='context',
                   features=np.array([
                       self._encode_workload(current_workload),  # 工况编码
                       step_count / 1000.0,  # 归一化步数
                       mode / 8.0,  # 归一化模式
                       0.0  # 预留
                   ], dtype=np.float32))

        # 将工况节点连接到所有控制相关节点
        # 连接到所有列节点
        for i in range(8):
            col_node = f'column_{i}'
            if col_node in G.nodes:
                G.add_edge('workload_info', col_node,
                           type='context',
                           features=np.array([
                               1.0,  # 关系强度
                               0.0  # 预留
                           ], dtype=np.float32))

        # 连接到端口节点
        for port in ['extract', 'raffinate', 'feed', 'desorbent']:
            if port in G.nodes:
                G.add_edge('workload_info', port,
                           type='context',
                           features=np.array([
                               1.0,  # 关系强度
                               0.0  # 预留
                           ], dtype=np.float32))

        # 连接到智能体节点
        agent_nodes = [node for node in G.nodes if G.nodes[node].get('type') == 'agent']
        for agent_node in agent_nodes:
            G.add_edge('workload_info', agent_node,
                       type='context',
                       features=np.array([
                           2.0,  # 较强的关系强度，因为工况对智能体决策更重要
                           0.0  # 预留
                       ], dtype=np.float32))

        # 更新图
        self.G = G

        # 转换为PyTorch Geometric格式
        return self._convert_to_pyg(G)

    def _estimate_flow_rate(self, col_idx, control_input, mode):
        """
        估计列间流速

        参数:
        -----
        col_idx: int
            列索引
        control_input: np.ndarray
            控制输入(4个区段流速)
        mode: int
            当前模式

        返回:
        -----
        flow_rate: float
            估计的流速
        """
        # 确定列在周期中的位置
        role = (col_idx - mode) % 8

        # 根据角色确定区段
        section = self._map_role_to_section(role)

        # 特殊情况: 列位于某区段末尾
        if role in [1, 3, 5, 7]:
            # 区段末尾，流速发生变化
            if role == 1:  # 区段1末尾(萃取口)
                return control_input[1]  # 区段2流速
            elif role == 3:  # 区段2末尾
                return control_input[2]  # 区段3流速
            elif role == 5:  # 区段3末尾(萃余口)
                return control_input[3]  # 区段4流速
            else:  # 区段4末尾
                return control_input[0]  # 区段1流速

        # 一般情况: 列位于区段中间
        if section == 1:
            return control_input[0]  # 区段1流速
        elif section == 2:
            return control_input[1]  # 区段2流速
        elif section == 3:
            return control_input[2]  # 区段3流速
        else:  # section == 4
            return control_input[3]  # 区段4流速

    def _map_role_to_section(self, role):
        """
        将列在周期中的位置映射到区段

        参数:
        -----
        role: int
            列在周期中的位置(0-7)

        返回:
        -----
        section: int
            对应的区段(1-4)
        """
        if role in [0, 1]:
            return 1  # 区段1
        elif role in [2, 3]:
            return 2  # 区段2
        elif role in [4, 5]:
            return 3  # 区段3
        else:  # role in [6, 7]
            return 4  # 区段4

    def _convert_to_pyg(self, G):
        """
        将NetworkX图转换为PyTorch Geometric数据

        参数:
        -----
        G: nx.DiGraph
            NetworkX图

        返回:
        -----
        data: torch_geometric.data.Data
            PyTorch Geometric格式的图数据
        """
        # 1. 创建节点映射(节点ID到索引)
        node_map = {node: i for i, node in enumerate(G.nodes)}

        # 2. 收集节点特征
        # 初始化节点特征矩阵
        node_features = torch.zeros((len(G.nodes), self._get_max_feature_dim(G, 'node')), dtype=torch.float)
        node_types = []

        # 填充节点特征
        for i, node in enumerate(G.nodes):
            node_data = G.nodes[node]
            node_type = node_data['type']
            features = node_data['features']

            # 记录节点类型
            node_types.append(node_type)

            # 将特征填充到节点特征矩阵
            feature_len = len(features)
            node_features[i, :feature_len] = torch.tensor(features, dtype=torch.float)

            # 添加时序特征(如果有)
            if 'temporal_features' in node_data:
                temporal_dim = len(node_data['temporal_features'][0])
                for t, temp_feat in enumerate(node_data['temporal_features']):
                    start_idx = feature_len + t * temporal_dim
                    end_idx = start_idx + temporal_dim
                    if end_idx <= node_features.shape[1]:
                        node_features[i, start_idx:end_idx] = torch.tensor(temp_feat, dtype=torch.float)

        # 3. 收集边索引和特征
        edge_index = []
        edge_features = []
        edge_types = []

        for src, dst, edge_data in G.edges(data=True):
            # 找到源节点和目标节点的索引
            src_idx = node_map[src]
            dst_idx = node_map[dst]

            # 记录边索引
            edge_index.append([src_idx, dst_idx])

            # 记录边类型
            edge_types.append(edge_data['type'])

            # 记录基本边特征
            features = edge_data['features']
            base_features = torch.tensor(features, dtype=torch.float)

            # 添加动态特征(如果有)
            if 'dynamic_features' in edge_data:
                dynamic_features = torch.tensor(edge_data['dynamic_features'], dtype=torch.float)
                # 连接基本特征和动态特征
                edge_feat = torch.cat([base_features, dynamic_features])
            else:
                # 只使用基本特征，并填充到最大维度
                edge_feat = torch.zeros(self._get_max_feature_dim(G, 'edge'), dtype=torch.float)
                edge_feat[:len(base_features)] = base_features

            edge_features.append(edge_feat)

        # 4. 转换为PyTorch张量
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        edge_features = torch.stack(edge_features) if edge_features else torch.zeros((0, 0), dtype=torch.float)

        # 5. 创建节点类型和边类型的映射
        node_type_map = {node_type: i for i, node_type in enumerate(set(node_types))}
        edge_type_map = {edge_type: i for i, edge_type in enumerate(set(edge_types))}

        # 将节点类型和边类型转换为索引
        node_type_indices = torch.tensor([node_type_map[t] for t in node_types], dtype=torch.long)
        edge_type_indices = torch.tensor([edge_type_map[t] for t in edge_types], dtype=torch.long)

        # 6. 创建PyTorch Geometric数据对象
        data = Data(
            x=node_features,
            edge_index=edge_index,
            edge_attr=edge_features,
            node_type=node_type_indices,
            edge_type=edge_type_indices,
            # 保存映射信息以便解释
            node_type_map=node_type_map,
            edge_type_map=edge_type_map,
            # 也保存原始节点名称
            node_names=list(G.nodes)
        )

        return data

    def _get_max_feature_dim(self, G, element_type):
        """
        获取图中节点或边的最大特征维度

        参数:
        -----
        G: nx.DiGraph
            NetworkX图
        element_type: str
            'node' 或 'edge'

        返回:
        -----
        max_dim: int
            最大特征维度
        """
        if element_type == 'node':
            # 基本特征最大维度
            base_max_dim = max([len(G.nodes[node]['features']) for node in G.nodes], default=0)

            # 如果有时序特征，计算最大总维度
            temporal_max_dim = 0
            for node in G.nodes:
                if 'temporal_features' in G.nodes[node]:
                    temporal_features = G.nodes[node]['temporal_features']
                    if temporal_features:
                        temporal_dim = len(temporal_features[0]) * len(temporal_features)
                        temporal_max_dim = max(temporal_max_dim, temporal_dim)

            return base_max_dim + temporal_max_dim

        else:  # edge
            # 基本特征最大维度
            base_max_dim = max([len(G.edges[edge]['features']) for edge in G.edges], default=0)

            # 如果有动态特征，计算最大总维度
            dynamic_max_dim = 0
            for edge in G.edges:
                if 'dynamic_features' in G.edges[edge]:
                    dynamic_max_dim = max(dynamic_max_dim, len(G.edges[edge]['dynamic_features']))

            return base_max_dim + dynamic_max_dim

    def _encode_workload(self, workload):
        """
        将工况编码为数值特征

        参数:
        -----
        workload: str
            工况描述

        返回:
        -----
        encoding: float
            工况的数值编码
        """
        # 为不同工况定义编码值
        workload_map = {
            'normal': 0.0,
            'high_feed': 1.0,
            'low_feed': -1.0,
            'disturbed': 0.5,
            'high_temperature': 0.8,
            'low_temperature': -0.8
        }

        # 返回对应编码，默认为0.0
        return workload_map.get(workload, 0.0)