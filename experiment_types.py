import os
import numpy as np
import torch
from experiment_runner import ExperimentRunner
from experiment_configs import *
from plotting import create_timestamp_folder, plot_pareto_analysis


def run_algorithm_comparison(num_runs=3, num_episodes=100, base_dir='experiments'):
    """比较不同强化学习算法的性能"""
    print("===== 开始执行算法对比实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'algorithm_comparison')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_algorithm_comparison_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印实验小结
    print("\n===== 算法对比实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    return runner


def run_gnn_comparison(num_runs=3, num_episodes=100, base_dir='experiments'):
    """比较不同GNN架构的性能"""
    print("===== 开始执行GNN架构对比实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'gnn_comparison')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_gnn_comparison_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印实验小结
    print("\n===== GNN架构对比实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    return runner


def run_agent_mode_comparison(num_runs=3, num_episodes=100, base_dir='experiments'):
    """比较不同智能体模式的性能"""
    print("===== 开始执行智能体模式对比实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'agent_mode_comparison')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_agent_mode_comparison_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印实验小结
    print("\n===== 智能体模式对比实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    return runner


def run_graph_strategy_comparison(num_runs=3, num_episodes=100, base_dir='experiments'):
    """比较不同图构建策略的性能"""
    print("===== 开始执行图构建策略对比实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'graph_strategy_comparison')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_graph_strategy_comparison_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印实验小结
    print("\n===== 图构建策略对比实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    return runner


def run_workload_adaptation_experiment(num_runs=3, num_episodes=100, base_dir='experiments'):
    """测试不同工况下的适应性能力"""
    print("===== 开始执行工况适应性实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'workload_adaptation')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_workload_adaptation_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印实验小结
    print("\n===== 工况适应性实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    return runner


def run_single_experiment(num_runs=1, num_episodes=100, base_dir='experiments'):
    """运行单次标准实验"""
    print("===== 开始执行单次标准实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'single')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_standard_experiment_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    return runner


def run_ablation_studies(num_runs=2, num_episodes=100, base_dir='experiments'):
    """运行消融实验"""
    print("===== 开始执行消融实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'ablation')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_ablation_experiments()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验（使用较少的episode和运行次数以便快速测试）
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=200,
                           save_interval=50, log_interval=1)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印结果摘要
    print("\n===== 消融实验结果摘要 =====")
    for name, exp_result in runner.results.items():
        print(f"\n实验: {name}")

        # 计算平均性能指标
        test_rewards = [run['test_reward'] for run in exp_result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in exp_result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in exp_result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in exp_result['runs']]

        # 计算稳态性能指标
        steady_state_steps = []
        steady_state_rewards = []
        steady_state_extract_purities = []
        steady_state_raffinate_purities = []
        steady_state_productivities = []

        for run in exp_result['runs']:
            if 'steady_state' in run:
                steady_state_steps.append(run['steady_state'].get('step', 0))
                steady_state_rewards.append(run['steady_state'].get('reward', 0))
                steady_state_extract_purities.append(run['steady_state'].get('extract_purity', 0))
                steady_state_raffinate_purities.append(run['steady_state'].get('raffinate_purity', 0))
                steady_state_productivities.append(run['steady_state'].get('productivity', 0))

        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

        if steady_state_steps:
            print(f"\n稳态性能 (平均从第{np.mean(steady_state_steps):.1f}步开始):")
            print(f"  稳态奖励: {np.mean(steady_state_rewards):.4f} ± {np.std(steady_state_rewards):.4f}")
            print(
                f"  稳态萃取纯度: {np.mean(steady_state_extract_purities):.4f} ± {np.std(steady_state_extract_purities):.4f}")
            print(
                f"  稳态萃余纯度: {np.mean(steady_state_raffinate_purities):.4f} ± {np.std(steady_state_raffinate_purities):.4f}")
            print(
                f"  稳态生产率: {np.mean(steady_state_productivities):.6f} ± {np.std(steady_state_productivities):.6f}")

    return runner


def run_comprehensive_experiment(num_runs=5, num_episodes=150, base_dir='experiments'):
    """运行全面实验"""
    print("===== 开始执行全面实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'comprehensive')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_comprehensive_experiment_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500,
                           save_interval=50, log_interval=1)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 打印详细的结果摘要
    print("\n===== 全面实验结果摘要 =====")

    # 创建比较表格
    comparison_table = []
    headers = ["Test", "Extraction purity", "Residual purity", "Productivity", "Reward", "Steady-state extraction purity",
               "Steady-state residual purity", "Steady-state achievement steps"]
    comparison_table.append(headers)

    for name, exp_result in runner.results.items():
        # 计算平均性能指标
        test_rewards = [run['test_reward'] for run in exp_result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in exp_result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in exp_result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in exp_result['runs']]
        training_times = [run['training_time'] for run in exp_result['runs']]

        # 计算稳态性能指标
        steady_state_steps = []
        steady_state_extract_purities = []
        steady_state_raffinate_purities = []

        for run in exp_result['runs']:
            if 'steady_state' in run:
                steady_state_steps.append(run['steady_state'].get('step', 0))
                steady_state_extract_purities.append(run['steady_state'].get('extract_purity', 0))
                steady_state_raffinate_purities.append(run['steady_state'].get('raffinate_purity', 0))

        # 添加到表格
        row = [
            name,
            f"{np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}",
            f"{np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}",
            f"{np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}",
            f"{np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}"
        ]

        if steady_state_steps:
            row.extend([
                f"{np.mean(steady_state_extract_purities):.4f} ± {np.std(steady_state_extract_purities):.4f}",
                f"{np.mean(steady_state_raffinate_purities):.4f} ± {np.std(steady_state_raffinate_purities):.4f}",
                f"{np.mean(steady_state_steps):.1f} ± {np.std(steady_state_steps):.1f}"
            ])
        else:
            row.extend(["N/A", "N/A", "N/A"])

        comparison_table.append(row)

        # 详细打印
        print(f"\n实验: {name}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")
        print(f"奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"训练时间: {np.mean(training_times):.1f}s ± {np.std(training_times):.1f}s")

        if steady_state_steps:
            print(f"\n稳态性能 (平均从第{np.mean(steady_state_steps):.1f}步开始):")
            print(
                f"  稳态萃取纯度: {np.mean(steady_state_extract_purities):.4f} ± {np.std(steady_state_extract_purities):.4f}")
            print(
                f"  稳态萃余纯度: {np.mean(steady_state_raffinate_purities):.4f} ± {np.std(steady_state_raffinate_purities):.4f}")

    # 打印表格
    print("\n性能指标比较表:")
    for row in comparison_table:
        print(" | ".join(str(cell).ljust(15) for cell in row))

    # 保存比较表格
    with open(os.path.join(log_dir, 'performance_comparison.txt'), 'w') as f:
        for row in comparison_table:
            f.write(" | ".join(str(cell).ljust(15) for cell in row) + "\n")

    # 计算性能提升幅度
    if len(runner.results) >= 2:
        print("\n性能提升分析:")
        baseline_name = 'baseline_no_graph'
        optimal_name = 'optimal_config'

        if baseline_name in runner.results and optimal_name in runner.results:
            baseline_results = runner.results[baseline_name]['runs']
            optimal_results = runner.results[optimal_name]['runs']

            # 计算全过程平均性能
            baseline_extract = np.mean([run.get('test_extract_purity', 0) for run in baseline_results])
            baseline_raffinate = np.mean([run.get('test_raffinate_purity', 0) for run in baseline_results])
            baseline_reward = np.mean([run['test_reward'] for run in baseline_results])

            optimal_extract = np.mean([run.get('test_extract_purity', 0) for run in optimal_results])
            optimal_raffinate = np.mean([run.get('test_raffinate_purity', 0) for run in optimal_results])
            optimal_reward = np.mean([run['test_reward'] for run in optimal_results])

            # 计算稳态性能
            baseline_steady_extract = np.mean(
                [run['steady_state'].get('extract_purity', 0) for run in baseline_results if 'steady_state' in run])
            baseline_steady_raffinate = np.mean(
                [run['steady_state'].get('raffinate_purity', 0) for run in baseline_results if 'steady_state' in run])

            optimal_steady_extract = np.mean(
                [run['steady_state'].get('extract_purity', 0) for run in optimal_results if 'steady_state' in run])
            optimal_steady_raffinate = np.mean(
                [run['steady_state'].get('raffinate_purity', 0) for run in optimal_results if 'steady_state' in run])

            # 计算稳态达成时间
            baseline_steady_steps = np.mean(
                [run['steady_state'].get('step', 0) for run in baseline_results if 'steady_state' in run])
            optimal_steady_steps = np.mean(
                [run['steady_state'].get('step', 0) for run in optimal_results if 'steady_state' in run])

            # 计算提升幅度
            print(f"全过程平均性能提升:")
            print(f"  萃取纯度提升: {(optimal_extract - baseline_extract) / baseline_extract * 100:.2f}%")
            print(f"  萃余纯度提升: {(optimal_raffinate - baseline_raffinate) / baseline_raffinate * 100:.2f}%")
            print(f"  奖励提升: {(optimal_reward - baseline_reward) / abs(baseline_reward) * 100:.2f}%")

            if baseline_steady_extract > 0 and optimal_steady_extract > 0:
                print(f"\n稳态性能提升:")
                print(
                    f"  稳态萃取纯度提升: {(optimal_steady_extract - baseline_steady_extract) / baseline_steady_extract * 100:.2f}%")
                print(
                    f"  稳态萃余纯度提升: {(optimal_steady_raffinate - baseline_steady_raffinate) / baseline_steady_raffinate * 100:.2f}%")
                print(
                    f"  稳态达成速度提升: {(baseline_steady_steps - optimal_steady_steps) / baseline_steady_steps * 100:.2f}%")

    return runner


def run_pareto_experiment(num_runs=3, num_episodes=200, base_dir='experiments'):
    """比较不同奖励权重配置的性能（帕累托前沿分析）"""
    print("===== 开始执行帕累托前沿分析实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, 'pareto_analysis')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_pareto_experiment_config()
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=500)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    # 绘制帕累托前沿分析图
    pareto_plot = plot_pareto_analysis(runner.results, plots_dir)

    # 打印结果摘要
    print("\n===== 帕累托前沿分析实验结果摘要 =====")
    for name, result in runner.results.items():
        print(f"\n实验: {name}")
        # 提取测试指标
        test_rewards = [run.get('test_reward', 0) for run in result['runs']]
        test_extract_purities = [run.get('test_extract_purity', 0) for run in result['runs']]
        test_raffinate_purities = [run.get('test_raffinate_purity', 0) for run in result['runs']]
        test_productivities = [run.get('test_productivity', 0) for run in result['runs']]

        # 获取配置权重
        config = result['config']
        purity_weight = config.get('purity_weight', 0.5)
        productivity_weight = config.get('productivity_weight', 0.5)

        print(f"奖励权重: 纯度{purity_weight:.1f}/生产率{productivity_weight:.1f}")
        print(f"测试奖励: {np.mean(test_rewards):.4f} ± {np.std(test_rewards):.4f}")
        print(f"萃取纯度: {np.mean(test_extract_purities):.4f} ± {np.std(test_extract_purities):.4f}")
        print(f"萃余纯度: {np.mean(test_raffinate_purities):.4f} ± {np.std(test_raffinate_purities):.4f}")
        print(f"生产率: {np.mean(test_productivities):.6f} ± {np.std(test_productivities):.6f}")

    print(f"\n帕累托前沿分析图已保存至: {pareto_plot}")

    return runner


def run_mini_experiment(experiment_type='algorithm', num_runs=1, num_episodes=100, base_dir='experiments'):
    """运行迷你版实验（更少的episode和运行次数）"""
    print(f"===== 开始执行迷你版{experiment_type}实验 =====")

    # 创建实验运行器
    log_dir = os.path.join(base_dir, f'mini_{experiment_type}')
    runner = ExperimentRunner(base_log_dir=log_dir)

    # 添加实验配置
    configs = define_mini_experiment_config(experiment_type)
    for exp in configs:
        runner.add_experiment(exp['name'], exp['config'])

    # 运行实验
    runner.run_experiments(num_runs=num_runs, num_episodes=num_episodes, max_steps=200,
                           save_interval=20, log_interval=1)

    # 绘制结果
    plots_dir = create_timestamp_folder(os.path.join(log_dir, 'plots'))
    runner.export_results_summary(plots_dir)

    return runner